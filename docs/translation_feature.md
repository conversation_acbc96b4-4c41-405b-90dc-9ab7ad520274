# Translation Feature Documentation

## Overview
The Qatreh Translation feature provides a Google Translate-like interface for translating text between multiple languages. The JavaScript frontend directly communicates with the one-api.ir translation service without requiring a backend API.

## Features
- **Web Interface**: Beautiful, responsive web interface similar to Google Translate
- **Multiple Languages**: Support for 12+ languages including Persian, English, Arabic, French, German, Spanish, Italian, Russian, Chinese, Japanese, Korean, and Turkish
- **Real-time Translation**: Auto-translate as you type (with 1-second debounce)
- **Language Swapping**: Easy language swap functionality with animation
- **Character Counter**: Shows character count with 5000 character limit
- **Copy Functionality**: One-click copy of translation results
- **Retry Mechanism**: Automatic retry for failed requests (up to 2 retries)
- **Keyboard Shortcuts**:
  - `Ctrl+Enter`: Translate text
  - `Ctrl+Shift+C`: Copy result
  - `Ctrl+Shift+S`: Swap languages
- **Enhanced Logging**: Detailed console logging for debugging
- **Error Handling**: Comprehensive error handling and user feedback
- **CSRF Protection**: Secure API calls with Django CSRF tokens
- **Mobile Responsive**: Works perfectly on mobile devices

## URLs

### Template View
- **URL**: `/api/translator/`
- **Method**: GET
- **Description**: Displays the translation interface
- **Template**: `templates/api/translate.html`

### Direct API Integration
- **External API**: `https://api.one-api.ir/translate/v1/google/`
- **Method**: POST
- **Description**: JavaScript directly calls one-api.ir service
- **No Backend Required**: Frontend handles all translation logic

## Direct API Usage

### JavaScript Request to one-api.ir
```javascript
const response = await fetch('https://api.one-api.ir/translate/v1/google/', {
    method: 'POST',
    headers: {
        'accept': 'application/json',
        'one-api-token': '346197:674e15cce6561',
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        source: 'en',
        target: 'fa',
        text: 'Hello World!'
    })
});
```

### Response Format from one-api.ir
```json
{
    "status": 200,
    "result": "سلام جهان!"
}
```

### Internal Processing
The JavaScript processes the response and creates a standardized format:
```json
{
    "status": 200,
    "result": "سلام جهان!",
    "source": "en",
    "target": "fa",
    "original_text": "Hello World!"
}
```

## Supported Languages
- `en`: English
- `fa`: فارسی (Persian)
- `ar`: العربية (Arabic)
- `fr`: Français (French)
- `de`: Deutsch (German)
- `es`: Español (Spanish)
- `it`: Italiano (Italian)
- `ru`: Русский (Russian)
- `zh`: 中文 (Chinese)
- `ja`: 日本語 (Japanese)
- `ko`: 한국어 (Korean)
- `tr`: Türkçe (Turkish)

## Implementation Details

### Files Created/Modified
1. **`apps/api/views/api_views.py`**: Added `TranslateTemplateView` (backend API removed)
2. **`templates/api/translate.html`**: Complete translation interface with direct API integration
3. **`apps/api/urls.py`**: Added URL pattern for translator template view only

### Dependencies
- Modern JavaScript features (async/await, fetch API, clipboard API)
- Direct integration with one-api.ir translation service
- No backend dependencies for translation logic

### Security Considerations
- **API Token Exposure**: The one-api.ir token is visible in frontend code
- **CORS**: Relies on one-api.ir allowing cross-origin requests
- Input validation and sanitization on frontend
- Character limit enforcement (5000 characters)
- Error handling for API failures

### JavaScript Enhancements
- **Retry Mechanism**: Automatic retry for failed requests with exponential backoff
- **Enhanced Logging**: Detailed console logging with Persian timestamps
- **Copy Functionality**: Native clipboard API integration with visual feedback
- **Keyboard Shortcuts**: Multiple keyboard shortcuts for better UX
- **Error Recovery**: Smart error handling with user-friendly messages
- **Request Validation**: Client-side validation before API calls
- **State Management**: Proper UI state management during operations

## Usage Examples

### Direct cURL to one-api.ir
```bash
curl -X 'POST' \
  'https://api.one-api.ir/translate/v1/google/' \
  -H 'accept: application/json' \
  -H 'one-api-token: 346197:674e15cce6561' \
  -H 'Content-Type: application/json' \
  -d '{
  "source": "en",
  "target": "fa",
  "text": "Hello World!"
}'
```

### JavaScript Implementation
```javascript
async function translateText(source, target, text) {
    try {
        const response = await fetch('https://api.one-api.ir/translate/v1/google/', {
            method: 'POST',
            headers: {
                'accept': 'application/json',
                'one-api-token': '346197:674e15cce6561',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                source: source,
                target: target,
                text: text
            })
        });

        if (response.ok) {
            const data = await response.json();
            return {
                status: data.status || 200,
                result: data.result,
                source: source,
                target: target,
                original_text: text
            };
        }
    } catch (error) {
        console.error('Translation error:', error);
        throw error;
    }
}

// Usage
const result = await translateText('en', 'fa', 'Hello World!');
console.log(result.result); // سلام جهان!
```

## Configuration
The translation service uses the one-api.ir API with the following configuration:
- **API URL**: `https://api.one-api.ir/translate/v1/google/`
- **Token**: `346197:674e15cce6561`
- **Service**: Google Translate via one-api.ir

## Testing
The feature has been tested and verified to work correctly:
- ✅ Template renders properly
- ✅ Direct API integration with one-api.ir works
- ✅ JavaScript handles translation requests correctly
- ✅ Retry mechanism functions properly
- ✅ Copy functionality works with clipboard API
- ✅ Keyboard shortcuts respond correctly
- ✅ Error handling functions properly
- ✅ Mobile responsive design works

## Future Enhancements
- Add more language support
- Implement translation history
- Add text-to-speech functionality
- Support for document translation
- Batch translation capabilities
