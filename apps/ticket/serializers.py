
from rest_framework import serializers
from apps.deposit.models import DepositMembership
from apps.ticket.models import Ticket, TicketMessage
from apps.issues.models import ReportSubject
from utils import FileFieldSerializer


class TicketSerializer(serializers.ModelSerializer):
    is_read = serializers.SerializerMethodField()
    user = serializers.SerializerMethodField()
    last_message_date = serializers.DateTimeField(read_only=True)
    user_id = serializers.IntegerField(write_only=True, required=False)
    deposit_name = serializers.SerializerMethodField()
    report_subject_info = serializers.SerializerMethodField()
    subject_id = serializers.IntegerField(write_only=True, required=False)

    class Meta:
        model = Ticket
        fields = [
            'id', 'deposit', 'deposit_name', 'user', 'ticket_type', 'subject', 'description',
            'is_closed', 'closed_at', 'created_at', 'is_read', 'last_message_date',
            'user_id', 'subject_id', 'report_subject_info'
        ]
        read_only_fields = ['user', 'created_at', 'updated_at', 'is_closed', 'closed_at', 'is_read', 'last_message_date', 'report_subject_info']

    def get_is_read(self, obj):
        user = self.context['request'].user
        # بررسی اینکه آیا پیامی از طرف کاربران دیگر وجود دارد که خوانده نشده باشد
        unread_messages = obj.messages.exclude(user=user).filter(is_read=False).exists()
        return not unread_messages

    def get_user(self, obj):
        membership = DepositMembership.objects.filter(user=obj.user, deposit=obj.deposit).first()
        role = membership.role if membership else None
        fullname = (
            obj.deposit.title
            if role in ["Owner", "Admin"]
            else membership.user.fullname if membership else obj.user.get_full_name()
        )
        return {
            "id": obj.user.id,
            "fullname": fullname,
            "role": role
        }
    
    def get_deposit_name(self, obj):
        return obj.deposit.title if obj.deposit else None

    def get_report_subject_info(self, obj):
        """Get report subject information for report type tickets"""
        if obj.ticket_type == Ticket.TicketType.REPORT and obj.report_subject:
            return {
                'id': obj.report_subject.id,
                'title': obj.report_subject.title,
                'description': obj.report_subject.description
            }
        return None

    def validate(self, data):
        """Validate ticket data based on type"""
        ticket_type = data.get('ticket_type', Ticket.TicketType.TICKET)

        if ticket_type == Ticket.TicketType.TICKET:
            # For regular tickets, deposit is required
            if not data.get('deposit'):
                raise serializers.ValidationError("Deposit is required for ticket type.")
        elif ticket_type == Ticket.TicketType.REPORT:
            # For reports, subject_id is required
            subject_id = data.get('subject_id')
            if not subject_id:
                raise serializers.ValidationError("subject_id is required for report type.")

            try:
                report_subject = ReportSubject.objects.get(id=subject_id, is_active=True)
                data['report_subject'] = report_subject
            except ReportSubject.DoesNotExist:
                raise serializers.ValidationError("Invalid or inactive report subject.")

        return data


class TicketMessageSerializer(serializers.ModelSerializer):
    user = serializers.SerializerMethodField()
    is_self = serializers.SerializerMethodField()
    deposit_name = serializers.SerializerMethodField()
    content_image = FileFieldSerializer(required=False)
    sender_type = serializers.SerializerMethodField()
    sender_info = serializers.SerializerMethodField()

    class Meta:
        model = TicketMessage
        fields = ['id', 'ticket', 'user', 'content', 'message_type', 'content_image', 'is_read', 'created_at', 'is_self', 'deposit_name', 'sender_type', 'sender_info']
        read_only_fields = ['user', 'created_at', 'is_read', 'ticket', 'is_self', 'sender_type', 'sender_info']
        extra_kwargs = {
            'content': {'required': False, 'allow_blank': True, 'allow_null': True}
        }


    def get_user(self, obj):
        """Get user information for the message"""
        # Handle report type tickets differently
        if obj.ticket.ticket_type == Ticket.TicketType.REPORT:
            if obj.user == obj.ticket.user:
                # Message from the user who created the report
                return {
                    "id": obj.user.id,
                    "fullname": obj.user.fullname,
                    "role": "User"
                }
            else:
                # Message from support/admin - show as technical support
                return {
                    "id": obj.user.id,
                    "fullname": "پشتیبانی فنی",
                    "role": "Support"
                }

        # Handle regular ticket type
        if obj.ticket.deposit:
            membership = DepositMembership.objects.filter(user=obj.user, deposit=obj.ticket.deposit).first()
            role = membership.role if membership else None
            fullname = (
                obj.ticket.deposit.title
                if role in ["Owner", "Admin"]
                else membership.user.fullname if membership else obj.user.get_full_name()
            )
            return {
                "id": obj.user.id,
                "fullname": fullname,
                "role": role
            }

        # Fallback for tickets without deposit
        return {
            "id": obj.user.id,
            "fullname": obj.user.fullname,
            "role": "User"
        }

    def get_is_self(self, obj):
        """Check if the message is from the current user"""
        request_user = self.context.get('request').user
        if not request_user.is_authenticated:
            return False

        # For report type tickets, compare users directly
        if obj.ticket.ticket_type == Ticket.TicketType.REPORT:
            return obj.user == request_user

        # For regular tickets with deposit
        if obj.ticket.deposit:
            # Get the membership and role of the message creator
            obj_membership = DepositMembership.objects.filter(user=obj.user, deposit=obj.ticket.deposit).first()
            obj_role = obj_membership.role if obj_membership else None

            # Get the membership and role of the logged-in user
            request_membership = DepositMembership.objects.filter(user=request_user, deposit=obj.ticket.deposit).first()
            request_role = request_membership.role if request_membership else None

            # Check if both roles are admin/owner (treat Owner and Admin as equivalent)
            if obj.user == request_user and request_role in ["Owner", "Admin"]:
                return True

            # For other roles, compare them directly
            return obj_role == request_role

        # Fallback: direct user comparison
        return obj.user == request_user

    def get_deposit_name(self, obj):
        """Get deposit name for the ticket"""
        return obj.ticket.deposit.title if obj.ticket and obj.ticket.deposit else None

    def get_sender_type(self, obj):
        """Determine if sender is user or admin/owner"""
        # For report type tickets
        if obj.ticket.ticket_type == Ticket.TicketType.REPORT:
            if obj.user == obj.ticket.user:
                return "user"
            else:
                return "support"

        # For regular tickets with deposit
        if obj.ticket.deposit:
            membership = DepositMembership.objects.filter(user=obj.user, deposit=obj.ticket.deposit).first()
            role = membership.role if membership else None

            if role in ["Owner", "Admin"]:
                return "manager"
            else:
                return "user"

        return "user"

    def get_sender_info(self, obj):
        """Get sender information based on their role and ticket type"""
        # For report type tickets
        if obj.ticket.ticket_type == Ticket.TicketType.REPORT:
            if obj.user == obj.ticket.user:
                # Message from the user who created the report
                return {
                    "id": obj.user.id,
                    "name": obj.user.fullname,
                    "role": "User",
                    "type": "user"
                }
            else:
                # Message from support/admin
                return {
                    "id": obj.user.id,
                    "name": "پشتیبانی فنی",
                    "role": "Support",
                    "type": "support"
                }

        # For regular ticket type
        if obj.ticket.deposit:
            membership = DepositMembership.objects.filter(user=obj.user, deposit=obj.ticket.deposit).first()
            role = membership.role if membership else None

            if role in ["Owner", "Admin"]:
                # For managers, show deposit title and their role
                return {
                    "id": obj.user.id,
                    "name": obj.ticket.deposit.title,
                    "role": role,
                    "type": "manager"
                }
            else:
                # For users, show their actual name
                return {
                    "id": obj.user.id,
                    "name": obj.user.fullname,
                    "role": role or "Member",
                    "type": "user"
                }

        # Fallback
        return {
            "id": obj.user.id,
            "name": obj.user.fullname,
            "role": "User",
            "type": "user"
        }

    def validate(self, data):
        """Validate message type and content"""
        return data


class CloseTicketSerializer(serializers.Serializer):
    """Serializer for closing a ticket"""
    ticket_id = serializers.IntegerField()

    def validate_ticket_id(self, value):
        """Validate that ticket exists and user has permission to close it"""
        try:
            ticket = Ticket.objects.get(id=value)
        except Ticket.DoesNotExist:
            raise serializers.ValidationError("Ticket not found.")

        # Check if ticket is already closed
        # if ticket.is_closed:
            # raise serializers.ValidationError("Ticket is already closed.")

        # Check if user has permission to close the ticket
        # user = self.context['request'].user
        # user_tickets = Ticket.get_user_tickets(user)

        # if ticket not in user_tickets:
            # raise serializers.ValidationError("You don't have permission to close this ticket.")

        return value

    def save(self):
        """Close the ticket"""
        ticket_id = self.validated_data['ticket_id']
        ticket = Ticket.objects.get(id=ticket_id)
        ticket.close_ticket()
        return ticket



