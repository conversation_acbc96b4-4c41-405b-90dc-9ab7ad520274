from django.db import models

from django.utils.translation import gettext_lazy as _
from apps.account.models import User

class Ticket(models.Model):
    class TicketType(models.TextChoices):
        TICKET = 'ticket', _('Ticket')
        REPORT = 'report', _('Report')

    deposit = models.ForeignKey(
        "deposit.Deposit",
        on_delete=models.CASCADE,
        verbose_name=_("Deposit"),
        related_name="tickets",
        null=True,
        blank=True
    )
    user = models.ForeignKey(
        "account.User",
        on_delete=models.CASCADE,
        verbose_name=_("User"),
        related_name="tickets"
    )
    ticket_type = models.CharField(
        max_length=10,
        choices=TicketType.choices,
        default=TicketType.TICKET,
        verbose_name=_("Ticket Type")
    )
    report_subject = models.ForeignKey(
        "issues.ReportSubject",
        on_delete=models.CASCADE,
        verbose_name=_("Report Subject"),
        related_name="tickets",
        null=True,
        blank=True
    )
    subject = models.Char<PERSON>ield(max_length=255, verbose_name=_("Subject"))
    description = models.TextField(verbose_name=_("Description"))
    is_closed = models.BooleanField(default=False, verbose_name=_("Is Closed"))
    closed_at = models.DateTimeField(null=True, blank=True, verbose_name=_("Closed At"))
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    updated_at = models.DateTimeField(auto_now=True, verbose_name=_("Updated At"))

    def __str__(self):
        status = "Closed" if self.is_closed else "Open"
        return f"Ticket #{self.id} - {self.subject} ({status})"

    def close_ticket(self):
        """Close the ticket"""
        from django.utils import timezone
        self.is_closed = True
        self.closed_at = timezone.now()
        self.save()
        
    @classmethod
    def create_with_user(cls, deposit, subject, description, user, request_user, create_message=True):
        ticket = cls.objects.create(
            deposit=deposit,
            user=user,
            subject=subject,
            description=description
        )
        
        # ایجاد پیام اولیه اگر درخواست شده باشد
        if create_message:
            from apps.ticket.models import TicketMessage
            TicketMessage.objects.create(
                ticket=ticket,
                user=request_user,
                content=description,
                is_read=False
            )
            
        return ticket
        
    @classmethod
    def get_user_tickets(cls, user):
        from django.db.models import Q
        from apps.deposit.models import DepositMembership
        
        # تیکت‌هایی که کاربر ایجاد کرده
        user_tickets = Q(user=user)
        
        # تیکت‌هایی که مربوط به صندوق‌هایی است که کاربر مالک آنهاست
        admin_owner_tickets = Q(
            deposit__members__user=user, 
            deposit__members__role__in=[DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN]
        )
        
        # ترکیب هر دو شرط با OR
        return cls.objects.filter(user_tickets | admin_owner_tickets).distinct()
    
    @classmethod
    def count_unread_messages(cls, user):
        from django.db.models import Q
        
        # دریافت تیکت‌های مربوط به کاربر
        user_tickets = cls.get_user_tickets(user)
        
        # شمارش پیام‌های خوانده نشده در این تیکت‌ها
        from apps.ticket.models import TicketMessage
        unread_count = TicketMessage.objects.filter(
            ticket__in=user_tickets,  # فقط در تیکت‌های مربوط به کاربر
            is_read=False,  # فقط پیام‌های خوانده نشده
        ).exclude(
            user=user  # به جز پیام‌هایی که خود کاربر ارسال کرده
        ).count()
        
        return unread_count

    class Meta:
        verbose_name = _("Ticket")
        verbose_name_plural = _("Tickets")
        
        
class TicketMessage(models.Model):
    class MessageType(models.TextChoices):
        TEXT = 'text', _('Text')
        IMAGE = 'image', _('Image')

    ticket = models.ForeignKey(
        Ticket,
        on_delete=models.CASCADE,
        verbose_name=_("Ticket"),
        related_name="messages"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.CASCADE,
        verbose_name=_("User"),
        related_name="ticket_messages"
    )
    is_read = models.BooleanField(default=False, verbose_name=_("Is Read"))
    content = models.TextField(verbose_name=_("Content"), blank=True, null=True)
    message_type = models.CharField(
        max_length=10,
        choices=MessageType.choices,
        default=MessageType.TEXT,
        verbose_name=_("Message Type")
    )
    content_image = models.ImageField(
        upload_to='ticket_messages/%Y/%m/',
        null=True,
        blank=True,
        verbose_name=_("Content Image")
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name=_("Created At"))
    
    def __str__(self):
        self.ticket.deposit
        user_member = self.user.deposit_memberships.filter(deposit=self.ticket.deposit).first()
        return f"Message by {user_member.role}/{self.user.fullname} on Ticket #{self.ticket.id}"
    
    def save(self, *args, **kwargs):
        # Check if this is a new message (not an update)
        is_new = self.pk is None
        
        # Save the message first
        super().save(*args, **kwargs)
        
        if is_new:
            try:
                from apps.deposit.models import DepositMembership
                from apps.account.services import NotificationService
                import logging
                logger = logging.getLogger(__name__)
                
                deposit = self.ticket.deposit
                user_member = self.user.deposit_memberships.filter(deposit=deposit).first()
                
                # Determine who should receive the notification
                if user_member and user_member.role in [DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN]:
                    # If message is from owner/admin, notify the ticket creator
                    if self.user.id != self.ticket.user.id:  # Don't notify if owner/admin is also the ticket creator
                        print(f'--send_new_ticket_message_notification--01-> self.user:{self.user.id}/ticket.user.id: {self.ticket.user.id}')
                        NotificationService.send_new_ticket_message_notification([self.ticket.user.id])
                else:
                    # If message is from regular user, notify all owners and admins of the deposit
                    # EXCEPT the current user (in case they are both a regular user and an admin/owner)
                    admin_owner_memberships = DepositMembership.objects.filter(
                        deposit=deposit,
                        role__in=[DepositMembership.Role.OWNER, DepositMembership.Role.ADMIN],
                        is_active=True
                    ).exclude(user=self.user)  # Exclude the current user from receiving notifications
                    
                    admin_owner_ids = [membership.user_id for membership in admin_owner_memberships]
                    
                    if admin_owner_ids:
                        print(f'--send_new_ticket_message_notification--02-> self.user:{self.user.id}/admin_owner_ids: {admin_owner_ids}')
                        NotificationService.send_new_ticket_message_notification(admin_owner_ids)
            except Exception as e:
                # Log the error but don't prevent the message from being saved
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error sending ticket message notification: {str(e)}")

    class Meta:
        verbose_name = _("Ticket Message")
        verbose_name_plural = _("Ticket Messages")
