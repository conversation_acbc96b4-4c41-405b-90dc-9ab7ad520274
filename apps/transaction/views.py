from rest_framework import generics, permissions, filters, status
from rest_framework.response import Response
from django.db.models import Q
from django.utils.translation import gettext_lazy as _
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.exceptions import Validation<PERSON>rror
from datetime import datetime, timed<PERSON>ta

from .models import Transaction
from .serializers import TransactionListSerializer
from .doc import transaction_list_swagger


class TransactionListView(generics.ListAPIView):
    """
    API endpoint for listing user transactions.

    This endpoint returns a list of successful transactions for the authenticated user,
    including both deposits (income) and withdrawals.
    """
    serializer_class = TransactionListSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['created_at', 'amount']
    ordering = ['-created_at']  # Default ordering is newest first

    @transaction_list_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_queryset(self):
        user = self.request.user

        # Start with successful transactions by default
        queryset = Transaction.objects.filter(
            user=user,
            status=Transaction.TransactionStatus.SUCCESS
        )

        # Filter by transaction type if provided
        transaction_type = self.request.query_params.get('transaction_type')
        if transaction_type:
            queryset = queryset.filter(transaction_type=transaction_type)

        # Filter by deposit if provided
        deposit_id = self.request.query_params.get('deposit_id')
        if deposit_id:
            queryset = queryset.filter(deposit_id=deposit_id)

        # Filter by amount (similar to DepositListAPIView)
        amount_param = self.request.query_params.get('amount')
        if amount_param:
            try:
                # Extract operator and value (e.g., "10+" → 10 and '+')
                operator = amount_param[-1]
                amount_value = int(amount_param[:-1])

                # Convert Tomans to Rials (1 Toman = 10 Rials)
                amount_in_rials = amount_value * 10

                if operator == '+':
                    queryset = queryset.filter(amount__gt=amount_in_rials)
                elif operator == '-':
                    queryset = queryset.filter(amount__lt=amount_in_rials)
                else:
                    raise ValueError
            except (ValueError, IndexError):
                raise ValidationError({"amount": "Invalid format. Use format like '10+' or '5-'"})

        # Filter by date range
        date_filter = self.request.query_params.get('date_filter')
        if date_filter:
            now = datetime.now()
            if date_filter == 'yesterday':
                start_date = now - timedelta(days=1)
                queryset = queryset.filter(created_at__date=start_date.date())
            elif date_filter == 'last_week':
                start_date = now - timedelta(days=7)
                queryset = queryset.filter(created_at__gte=start_date)
            elif date_filter == 'last_month':
                start_date = now - timedelta(days=30)
                queryset = queryset.filter(created_at__gte=start_date)
            elif date_filter == 'last_three_months':
                start_date = now - timedelta(days=90)
                queryset = queryset.filter(created_at__gte=start_date)

        return queryset

