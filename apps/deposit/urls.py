
from django.urls import path
from . import views

app_name = 'deposit'

urlpatterns = [
    # API endpoints
    path('', views.DepositListAPIView.as_view(), name='deposit-list'),
    path('<int:pk>/', views.DepositDetailAPIView.as_view(), name='deposit-detail'),


    path('<int:deposit_id>/members/', views.DepositMembersListView.as_view(), name='deposit-member-list'),
    path('<int:deposit_id>/members/<int:member_id>/set-role/', views.SetMemberRoleView.as_view(), name='deposit-member-set-role'),
    path('<int:deposit_id>/members/<int:member_id>/transactions/', views.DepositTransactionMemberView.as_view(), name='deposit-transaction-member'),
    path('<int:deposit_id>/members/<int:member_id>/details/', views.DepositMemberDetailView.as_view(), name='deposit-member-details'),
    path('<int:deposit_id>/transactions/', views.DepositTransactionListView.as_view(), name='deposit-transactions'),

    path('<int:deposit_id>/media/', views.DepositMediaAPIView.as_view(), name='deposit-media'),

    path('<int:deposit_id>/media/create/', views.DepositMediaCreateAPIView.as_view(), name='deposit-media-create'),
    path('media/<int:deposit_media_id>/update/', views.DepositMediaUpdateAPIView.as_view(), name='deposit-media-update'),
    path('media/<int:deposit_media_id>/delete/', views.DepositMediaDeleteAPIView.as_view(), name='deposit-media-delete'),

    # User's deposits and requests combined view
    path('user/', views.UserDepositsAndRequestsView.as_view(), name='user-deposits-and-requests'),

]