from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable
from django.urls import reverse, path
from django.utils.html import format_html
from django.http import HttpResponseRedirect
from django.templatetags.static import static

from unfold.admin import ModelAdmin, TabularInline
from unfold.contrib.filters.admin import RangeDateFilter, BooleanRadioFilter
from unfold.decorators import display
from unfold.sections import TableSection

from apps.deposit.models import DepositMedia, DepositMediaImage

from utils.admin import project_admin_site

class DepositMediaImageInline(TabularInline):
    model = DepositMediaImage
    extra = 0
    fields = ('image', 'priority')
    can_delete = True
    show_change_link = True
    hide_title = True  # Unfold specific option to hide title row


class DepositMediaAdmin(ModelAdmin):
    list_display = (
        'subject', 
        'deposit', 
        'media_info',
        'deposit_info',
        'images_count',
        'jalali_created'
    )
    list_filter = (
        ('created_at', RangeDateFilter),
        'deposit',
    )
    search_fields = ('subject', 'deposit__title', 'description')
    readonly_fields = ('created_at', 'updated_at')
    inlines = [DepositMediaImageInline, ]
    compressed_fields = False
    list_filter_submit = True  # Enable submit button for filters
    # autocomplete_fields = ('deposit',)
    
    fieldsets = (
        (None, {
            'fields': ('deposit', 'subject', 'description')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    ordering = ('-created_at',)

    def get_queryset(self, request):
        return super().get_queryset(request).select_related('deposit').prefetch_related('images')
    
    @display(description='Media', header=True)
    def media_info(self, obj):
        return [
            obj.subject,
        ]
    
    @display(description='Deposit', header=True)
    def deposit_info(self, obj):        
        return [
            obj.deposit.title,
        ]
    
    @display(description=_('Created Date'))
    def jalali_created(self, obj):
        from utils.date_utils import format_jalali_date
        if obj.created_at:
            return format_jalali_date(obj.created_at, "%Y/%m/%d %H:%M")
        return "-"
    
    def images_count(self, obj):
        return obj.images.count()
    images_count.short_description = _('Images Count')
    
    @display(description='Status')
    def media_status(self, obj):
        has_images = obj.images.exists()
        label_class = 'success' if has_images else 'warning'
        status_text = _('Complete') if has_images else _('Incomplete')
        
        return {
            'label': status_text,
            'class': label_class
        }


class DepositMediaImagesTableSection(TableSection):
    verbose_name = _("Media Images")
    related_name = "images"
    height = 380
    fields = [
        "image_preview",
        "priority",
    ]

    @admin.display(description=_("Image"))
    def image_preview(self, instance):
        if instance.image:
            return format_html('<img src="{}" width="100" height="auto" />', instance.image.url)
        return _("No Image")


project_admin_site.register(DepositMedia, DepositMediaAdmin)
