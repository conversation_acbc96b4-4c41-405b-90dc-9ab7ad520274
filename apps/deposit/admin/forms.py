from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from decimal import Decimal

from apps.deposit.models import Deposit, PollDeposit, SavingDeposit, ReportingDeposit


class BaseDepositAdminForm(forms.ModelForm):
    """
    Base form for all deposit types with common validation.
    """
    
    class Meta:
        model = Deposit
        fields = '__all__'
    
    def clean(self):
        cleaned_data = super().clean()
        deposit_type = cleaned_data.get('deposit_type')
        
        # Common validations
        if not cleaned_data.get('title'):
            raise ValidationError({'title': _('عنوان صندوق الزامی است.')})

        if not cleaned_data.get('owner'):
            raise ValidationError({'owner': _('مالک صندوق الزامی است.')})
        
        # Type-specific validations
        if deposit_type == Deposit.DepositType.POLL:
            self._validate_poll_deposit(cleaned_data)
        elif deposit_type == Deposit.DepositType.SAVING:
            self._validate_saving_deposit(cleaned_data)
        elif deposit_type == Deposit.DepositType.REPORTING:
            self._validate_reporting_deposit(cleaned_data)
        
        return cleaned_data
    
    def _validate_poll_deposit(self, cleaned_data):
        """Validate Poll deposit specific fields."""
        total_debt_amount = cleaned_data.get('total_debt_amount')
        lottery_month_count = cleaned_data.get('lottery_month_count')
        unit_amount = cleaned_data.get('unit_amount')
        
        # بررسی فیلدهای الزامی
        if not total_debt_amount:
            raise ValidationError({
                'total_debt_amount': _('برای صندوق قرعه‌کشی، مبلغ کل الزامی است.')
            })

        if not lottery_month_count:
            raise ValidationError({
                'lottery_month_count': _('برای صندوق قرعه‌کشی، تعداد ماه قرعه‌کشی الزامی است.')
            })

        if total_debt_amount <= 0:
            raise ValidationError({
                'total_debt_amount': _('مبلغ کل باید بیشتر از صفر باشد.')
            })

        if lottery_month_count <= 0:
            raise ValidationError({
                'lottery_month_count': _('تعداد ماه قرعه‌کشی باید بیشتر از صفر باشد.')
            })
        
        # دریافت تعداد قرعه‌کشی در ماه
        lottery_per_month_count = cleaned_data.get('lottery_per_month_count', 1)
        if not lottery_per_month_count:
            lottery_per_month_count = 1

        # محاسبه خودکار unit_amount بر اساس فرمول جدید
        total_shares = lottery_month_count * lottery_per_month_count
        calculated_unit_amount = total_debt_amount / total_shares

        # اگر unit_amount وارد نشده، محاسبه خودکار
        if not unit_amount:
            cleaned_data['unit_amount'] = calculated_unit_amount
        else:
            # اگر وارد شده، بررسی صحت آن
            if abs(unit_amount - calculated_unit_amount) > Decimal('0.01'):
                raise ValidationError({
                    'unit_amount': _(
                        'مبلغ واحد سهم باید %(calculated_amount)s باشد '
                        '(محاسبه شده از: %(total_debt)s ÷ (%(lottery_months)s × %(lottery_per_month)s) = %(total_shares)s سهم)'
                    ) % {
                        'calculated_amount': f'{calculated_unit_amount:,.2f}',
                        'total_debt': f'{total_debt_amount:,.0f}',
                        'lottery_months': lottery_month_count,
                        'lottery_per_month': lottery_per_month_count,
                        'total_shares': total_shares
                    }
                })
    
    def _validate_saving_deposit(self, cleaned_data):
        """Validate Saving deposit specific fields."""
        unit_amount = cleaned_data.get('unit_amount')
        validity_duration = cleaned_data.get('validity_duration')
        
        # بررسی فیلدهای الزامی
        if not unit_amount:
            raise ValidationError({
                'unit_amount': _('برای صندوق پس‌انداز، مبلغ واحد سهم الزامی است.')
            })

        if unit_amount <= 0:
            raise ValidationError({
                'unit_amount': _('مبلغ واحد سهم باید بیشتر از صفر باشد.')
            })

        # اگر validity_duration نداشته باشد، پیش‌فرض قرار دهیم
        if not validity_duration:
            cleaned_data['validity_duration'] = 24  # پیش‌فرض 2 سال
        elif validity_duration <= 0:
            raise ValidationError({
                'validity_duration': _('مدت اعتبار باید بیشتر از صفر باشد.')
            })
    
    def _validate_reporting_deposit(self, cleaned_data):
        """Validate Reporting deposit specific fields."""
        unit_amount = cleaned_data.get('unit_amount')
        
        # اگر unit_amount نداشته باشد، پیش‌فرض قرار دهیم
        if not unit_amount:
            cleaned_data['unit_amount'] = Decimal('100000.00')  # پیش‌فرض 100 هزار تومان
        elif unit_amount <= 0:
            raise ValidationError({
                'unit_amount': _('مبلغ واحد سهم باید بیشتر از صفر باشد.')
            })


class PollDepositAdminForm(BaseDepositAdminForm):
    """
    Specialized form for Poll deposits with enhanced validation.
    """
    
    class Meta:
        model = PollDeposit
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تنظیم فیلدهای مخصوص Poll
        self.fields['deposit_type'].initial = Deposit.DepositType.POLL
        self.fields['deposit_type'].widget = forms.HiddenInput()
        
        # فیلدهای غیرضروری را مخفی کنیم
        if 'start_date' in self.fields:
            self.fields['start_date'].widget = forms.HiddenInput()
        if 'validity_duration' in self.fields:
            self.fields['validity_duration'].widget = forms.HiddenInput()
        
        # اضافه کردن help text
        self.fields['total_debt_amount'].help_text = _('کل مبلغ صندوق که بین اعضا تقسیم می‌شود')
        self.fields['lottery_month_count'].help_text = _('تعداد ماه‌هایی که قرعه‌کشی انجام می‌شود')
        self.fields['unit_amount'].help_text = _('مبلغ ماهانه هر عضو (خودکار محاسبه می‌شود)')


class SavingDepositAdminForm(BaseDepositAdminForm):
    """
    Specialized form for Saving deposits.
    """
    
    class Meta:
        model = SavingDeposit
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تنظیم فیلدهای مخصوص Saving
        self.fields['deposit_type'].initial = Deposit.DepositType.SAVING
        self.fields['deposit_type'].widget = forms.HiddenInput()
        
        # فیلدهای غیرضروری را مخفی کنیم
        if 'initial_lottery_date' in self.fields:
            self.fields['initial_lottery_date'].widget = forms.HiddenInput()
        if 'lottery_month_count' in self.fields:
            self.fields['lottery_month_count'].widget = forms.HiddenInput()
        
        # اضافه کردن help text
        self.fields['unit_amount'].help_text = _('مبلغ ماهانه پس‌انداز هر عضو')
        self.fields['validity_duration'].help_text = _('مدت زمان پس‌انداز به ماه (پیش‌فرض: 24 ماه)')


class ReportingDepositAdminForm(BaseDepositAdminForm):
    """
    Specialized form for Reporting deposits.
    """
    
    class Meta:
        model = ReportingDeposit
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # تنظیم فیلدهای مخصوص Reporting
        self.fields['deposit_type'].initial = Deposit.DepositType.REPORTING
        self.fields['deposit_type'].widget = forms.HiddenInput()
        
        # فیلدهای غیرضروری را مخفی کنیم
        if 'start_date' in self.fields:
            self.fields['start_date'].widget = forms.HiddenInput()
        if 'validity_duration' in self.fields:
            self.fields['validity_duration'].widget = forms.HiddenInput()
        if 'lottery_month_count' in self.fields:
            self.fields['lottery_month_count'].widget = forms.HiddenInput()
        if 'initial_lottery_date' in self.fields:
            self.fields['initial_lottery_date'].widget = forms.HiddenInput()
        
        # اضافه کردن help text
        self.fields['unit_amount'].help_text = _('مبلغ ثابت گزارشی (پیش‌فرض: 100,000 تومان)')
