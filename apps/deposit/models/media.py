from django.db import models
from django.utils.translation import gettext_lazy as _
from filer.fields.image import Filer<PERSON><PERSON><PERSON>ield
from apps.deposit.models import Deposit, DepositMembership



class DepositMedia(models.Model):
    deposit = models.ForeignKey(
        Deposit, 
        on_delete=models.CASCADE, 
        related_name='media', 
        verbose_name=_("Deposit"), 
        help_text=_("The deposit associated with this media.")
    )
    subject = models.CharField(
        max_length=255, 
        verbose_name=_("Subject"), 
        help_text=_("The subject or title of the media.")
    )
    description = models.TextField(
        verbose_name=_("Description"), 
        help_text=_("A detailed description of the media."), 
        null=True, 
        blank=True
    )
    created_at = models.DateTimeField(
        auto_now_add=True, 
        verbose_name=_("Created At"), 
        help_text=_("The date and time when the media was created.")
    )
    updated_at = models.DateTimeField(
        auto_now=True, 
        verbose_name=_("Updated At"), 
        help_text=_("The date and time when the media was last updated.")
    )

    class Meta:
        verbose_name = _("Deposit Media")
        verbose_name_plural = _("Deposit Media")

    def __str__(self):
        return f"Media #{self.id} - {self.subject}"
    


class DepositMediaImage(models.Model):
    deposit_media = models.ForeignKey(
        DepositMedia, 
        on_delete=models.CASCADE, 
        related_name='images', 
        verbose_name=_("Deposit Media"), 
        help_text=_("The media associated with this image.")
    )
    image =  models.ImageField(
        upload_to='deposit_media/', null=True, blank=True,
        verbose_name=_('image')
    )
    priority = models.IntegerField(
        default=0, 
        verbose_name=_("Priority"),
        help_text=_("Priority of the image, lower values mean higher priority.")
    )

    class Meta:
        verbose_name = _('Merchant Image')
        verbose_name_plural = _('Merchant Images')
        ordering = ('priority',)

    def __str__(self):
        return f'{self.deposit_media.subject}-{self.id}'

            
    def save(self, *args, **kwargs):
        from django.db.models import F
        if DepositMediaImage.objects.filter(deposit_media=self.deposit_media, priority=self.priority).exists():
            DepositMediaImage.objects.filter(
                deposit_media=self.deposit_media,
                priority__gte=self.priority
            ).update(priority=F('priority') + 1)

        super().save(*args, **kwargs)
        
        
