from django.db import models
from django.utils import timezone
from dateutil.relativedelta import relativedelta

from apps.account.models import User
from django.utils.translation import gettext_lazy as _
from apps.region.models import Region


class Deposit(models.Model):
    class DepositType(models.TextChoices):
        POLL = "Poll", _("Poll Deposit")
        SAVING = "Saving", _("Saving Account")
        REPORTING = "Reporting", _("Reporting Account")

    deposit_type = models.CharField(
        max_length=20,
        choices=DepositType.choices,
        verbose_name=_("Deposit Type")
    )
    
    owner = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("Owner"), related_name="deposits")
    region = models.ForeignKey(Region, on_delete=models.CASCADE, verbose_name=_("Region"), related_name="region_deposits", help_text=_("The region to which this deposit belongs."))
    
    title = models.CharField(max_length=255, verbose_name=_('Title'))
    description = models.TextField(verbose_name=_('Deposit Description'))
    
    total_debt_amount = models.DecimalField(max_digits=20, decimal_places=2, verbose_name=_("Total Debt Amount"), null=True, blank=True) # مبلغ کل وام
    

    lottery_month_count = models.IntegerField(verbose_name=_("Number of Lottery Months"), null=True, blank=True) # تعداد ماه قرعه کشی

    lottery_per_month_count = models.IntegerField(verbose_name=_("Number of Lotteries Per Month"), null=True, blank=True, default=1) # تعداد قرعه کشی در ماه

    unit_amount = models.DecimalField(max_digits=20, decimal_places=2, verbose_name=_("Unit Shares Amount")) # مبلغ هر سهم
    
    payment_cycle = models.IntegerField(verbose_name=_("Payment Cycle (in months)")) # سر رسید پرداخت
    
    max_unit_per_request = models.IntegerField(verbose_name=_("Maximum Unit Shares Per Request")) # خداکثر  سهم درخواستی
    
    max_members_count = models.IntegerField(verbose_name=_("Maximum Number of Members")) # حداکثر تعداد اعضا
    
    validity_duration =  models.IntegerField(verbose_name=_("Validity Duration (in months)"), null=True, blank=True) # مدت اعتبار صندوق
    
    initial_lottery_date = models.DateField(verbose_name=_("Initial Lottery Date"), null=True, blank=True) # تاریخ شروع قرعه کشی
    
    start_date = models.DateField(verbose_name=_("Start Date"), null=True, blank=True) # تاریخ شروع صندوق
    
    rules = models.JSONField(verbose_name=_('Deposit Rules'), default=dict)

    is_active = models.BooleanField(verbose_name=_("Is Active"), default=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.title
    
        
 
    def get_completion_units(self):
        """
        Calculate the total number of shares filled by members.
        """
        filled_shares = self.members.aggregate(total_filled_shares=models.Sum('requested_unit_count'))['total_filled_shares']
        return filled_shares or 0 
    @property
    def total_unit_amount(self):
        """
        محاسبه تعداد کل سهم‌ها بر اساس نوع صندوق:

        صندوق قرعه‌کشی: تعداد ماه‌های قرعه‌کشی × تعداد قرعه‌کشی در ماه
        صندوق پس‌انداز/گزارشی: 0 (محدودیت ندارند)
        """
        if self.deposit_type == 'Reporting':
            return 0
        elif self.deposit_type == 'Poll':
            if self.lottery_month_count and self.lottery_per_month_count:
                return self.lottery_month_count * self.lottery_per_month_count
            elif self.lottery_month_count:
                # fallback to old calculation if lottery_per_month_count is not set
                return self.total_debt_amount / self.unit_amount if self.unit_amount else 0
            else:
                return 0
        else:  # Saving deposit
            return 0
    
    def has_capacity(self, requested_units):
        if requested_units <= 0:
            raise ValueError("Requested units must be greater than zero.")
        
        filled_shares = self.get_completion_units()
        total_unit_amount = self.total_unit_amount
        
        return (filled_shares + requested_units) <= total_unit_amount


    @classmethod
    def disable_previous_month_due_dates_for_all(cls):
        today = timezone.now().date()
        previous_month = today - relativedelta(months=1)
        
        # یافتن تمام سررسیدهای ماه گذشته که هنوز کامل نشدهاند
        due_dates = DepositDueDate.objects.filter(
            deposit__is_active=True,
            due_date__year=previous_month.year,
            due_date__month=previous_month.month,
            is_completed=False
        )
        
        # غیرفعال کردن دستهای سررسیدها
        due_dates.update(is_completed=True)
        
    def disable_previous_month_due_dates(self):
        """
        غیرفعال کردن سررسیدهای مربوط به ماه گذشته
        """
        today = timezone.now().date()
        # previous_month_date = today - relativedelta(months=1)
        # print(f'--previous_month_date-> {previous_month_date}')
        due_dates = self.due_dates.filter(
            due_date__lt=today, 
            is_completed=False
        )
        
        # بهروزرسانی وضعیت سررسیدها
        due_dates.update(is_completed=True)
        
    def get_current_due_date(self):
        today = timezone.now().date()
        current_due_date = self.due_dates.filter(
            # due_date__lte=today,
            is_completed=False
        ).order_by('due_date').first()  
        return current_due_date
    
    def soft_delete(self):
        """
        حذف نرم صندوق با غیرفعال کردن آن
        """
        self.is_active = False
        self.save()
        
    def clean(self):
        """
        Validate deposit fields before saving.
        """
        from django.core.exceptions import ValidationError

        # بررسی فیلدهای الزامی برای صندوق قرعه‌کشی
        if self.deposit_type == self.DepositType.POLL:
            if not self.total_debt_amount:
                raise ValidationError(_("برای صندوق قرعه‌کشی، مبلغ کل الزامی است."))
            if not self.lottery_month_count:
                raise ValidationError(_("برای صندوق قرعه‌کشی، تعداد ماه قرعه‌کشی الزامی است."))
            if not self.lottery_per_month_count or self.lottery_per_month_count <= 0:
                raise ValidationError(_("برای صندوق قرعه‌کشی، تعداد قرعه‌کشی در ماه باید حداقل 1 باشد."))

            # بررسی منطقی بودن تعداد کل سهم‌ها
            total_shares = self.lottery_month_count * self.lottery_per_month_count
            if self.total_debt_amount / total_shares < 1000:  # حداقل 1000 تومان هر سهم
                raise ValidationError(_(
                    "مبلغ هر سهم خیلی کم است (%(amount)s تومان). "
                    "لطفاً مبلغ کل را افزایش دهید یا تعداد سهم‌ها را کاهش دهید."
                ) % {'amount': int(self.total_debt_amount / total_shares)})

        # بررسی فیلدهای الزامی برای صندوق پس‌انداز
        elif self.deposit_type == self.DepositType.SAVING:
            if not self.unit_amount:
                raise ValidationError(_("برای صندوق پس‌انداز، مبلغ واحد سهم الزامی است."))

        # بررسی فیلدهای الزامی برای صندوق گزارشی
        elif self.deposit_type == self.DepositType.REPORTING:
            if not self.unit_amount:
                raise ValidationError(_("برای صندوق گزارشی، مبلغ واحد سهم الزامی است."))

    def save(self, *args, **kwargs):
        print(f'-deposit-save-modele->')
        # اجرای validation قبل از ذخیره
        self.clean()
        super().save(*args, **kwargs)

class PollDeposit(Deposit):
    class Meta:
        proxy = True
        verbose_name = "Poll Deposit"
        verbose_name_plural = "Poll Deposits"
        

    def save(self, *args, **kwargs):
        print('--poll-deposit-save-modele->')
        self.deposit_type = self.DepositType.POLL
        self.start_date = None
        self.validity_duration = None

        # تنظیم پیش‌فرض تعداد قرعه‌کشی در ماه
        if not self.lottery_per_month_count:
            self.lottery_per_month_count = 1

        # محاسبه خودکار unit_amount برای صندوق قرعه‌کشی
        if not self.unit_amount and self.total_debt_amount and self.lottery_month_count and self.lottery_per_month_count:
            self.unit_amount = self.calculate_unit_amount()

        super().save(*args, **kwargs)
                
    
    def calculate_unit_amount(self):
        """
        محاسبه مبلغ هر سهم بر اساس فرمول جدید:
        مبلغ هر سهم = مبلغ کل وام ÷ تعداد کل سهم‌ها
        تعداد کل سهم‌ها = تعداد ماه‌های قرعه‌کشی × تعداد قرعه‌کشی در ماه

        مثال:
        total_debt_amount = 12,000,000
        lottery_month_count = 12
        lottery_per_month_count = 2
        تعداد کل سهم‌ها = 12 × 2 = 24
        مبلغ هر سهم = 12,000,000 ÷ 24 = 500,000
        """
        if not self.lottery_month_count or not self.lottery_per_month_count:
            return self.total_debt_amount / self.lottery_month_count  # fallback to old formula

        total_shares = self.lottery_month_count * self.lottery_per_month_count
        return self.total_debt_amount / total_shares

    def can_perform_lottery_this_month(self):
        """
        بررسی اینکه آیا می‌توان در این ماه قرعه‌کشی انجام داد یا خیر
        بر اساس تعداد قرعه‌کشی‌های انجام شده در ماه جاری
        """
        if self.deposit_type != self.DepositType.POLL:
            return False

        if not self.lottery_per_month_count:
            return False

        from datetime import datetime
        from apps.lottery.models import DepositLottery

        current_month = datetime.now().month
        current_year = datetime.now().year

        # شمارش قرعه‌کشی‌های انجام شده در ماه جاری
        current_month_lotteries = DepositLottery.objects.filter(
            deposit=self,
            created_at__month=current_month,
            created_at__year=current_year
        ).count()

        return current_month_lotteries < self.lottery_per_month_count

    def get_total_shares_info(self):
        """
        اطلاعات کامل تعداد سهم‌ها برای صندوق قرعه‌کشی
        """
        if self.deposit_type != self.DepositType.POLL:
            return None

        return {
            'lottery_month_count': self.lottery_month_count,
            'lottery_per_month_count': self.lottery_per_month_count,
            'total_shares': self.total_unit_amount,
            'unit_amount': self.unit_amount,
            'total_debt_amount': self.total_debt_amount,
            'formula': f"{self.lottery_month_count} ماه × {self.lottery_per_month_count} قرعه = {self.total_unit_amount} سهم"
        }
        
    
    
    
class SavingDeposit(Deposit):
    class Meta:
        proxy = True
        verbose_name = "Saving Deposit"
        verbose_name_plural = "Saving Deposits"

    def save(self, *args, **kwargs):
        print('--saving-deposit-save-modele->')
        self.deposit_type = self.DepositType.SAVING
        self.initial_lottery_date = None
        self.lottery_month_count = None
        self.lottery_per_month_count = None  # صندوق پس‌انداز قرعه‌کشی ندارد

        # برای صندوق پس‌انداز، unit_amount باید دستی تنظیم شود
        # اما اگر validity_duration نداشته باشد، پیش‌فرض 24 ماه قرار می‌دهیم
        if not self.validity_duration:
            self.validity_duration = 24  # پیش‌فرض 2 سال

        super().save(*args, **kwargs)

        
class ReportingDeposit(Deposit):
    class Meta:
        proxy = True
        verbose_name = "Reporting Deposit"
        verbose_name_plural = "Reporting Deposits"
        
    def save(self, *args, **kwargs):
        print('---deposit-save-modele->')
        self.deposit_type = self.DepositType.REPORTING
        self.start_date = None
        self.validity_duration = None
        self.lottery_month_count = None
        self.lottery_per_month_count = None  # صندوق گزارشی قرعه‌کشی ندارد

        # برای صندوق گزارشی، اگر unit_amount نداشته باشد، مقدار پیش‌فرض قرار می‌دهیم
        if not self.unit_amount:
            from decimal import Decimal
            self.unit_amount = Decimal('100000.00')  # پیش‌فرض 100 هزار تومان

        super().save(*args, **kwargs)

    
    
    
    
    
class DepositMembership(models.Model):
    class Role(models.TextChoices):
        OWNER = "Owner", _("Owner")
        MEMBER = "Member", _("Member")
        ADMIN = "Admin", _("Admin")
        
    user = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name=_("User"), related_name="deposit_memberships")
    deposit = models.ForeignKey(Deposit, on_delete=models.CASCADE, verbose_name=_("Deposit"), related_name="members")
    role = models.CharField(max_length=20, choices=Role.choices, default=Role.MEMBER, verbose_name=_("Role"))
 
    requested_unit_count = models.IntegerField(
        verbose_name=_("Requested Unit Count"), 
        help_text=_("The number of unit shares requested by the user."),
        null=True, blank=True
    )        
    monthly_installment_amount = models.DecimalField(
        max_digits=20, 
        decimal_places=0, 
        verbose_name=_("Monthly Installment Amount"), 
        help_text=_("The monthly installment amount for the user."),
        null=True, blank=True
    )

    is_active = models.BooleanField(verbose_name=_("Is Active"), default=True)
    joined_date = models.DateTimeField(auto_now_add=True, verbose_name=_("Joined Date"))

    class Meta:
        unique_together = ("user", "deposit")
        verbose_name = _("Membership")
        verbose_name_plural = _("Memberships")

    @property
    def get_user(self):
        return self.user
    


