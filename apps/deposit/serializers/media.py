import os
from django.shortcuts import get_object_or_404
from utils import FileFieldSerializer, absolute_url
from utils.save_to_filer import import_file
from django.utils.translation import gettext_lazy as _
# from dj_filer.admin import get_thumbs
from utils import get_thumbs

from dj_language.models import Language
from django.utils import timezone
from rest_framework import serializers, status
from apps.deposit.models import DepositMedia, DepositMediaImage




class MediaImageSerializer(serializers.ModelSerializer):
    image_url = serializers.SerializerMethodField()

    class Meta:
        model = DepositMediaImage
        fields = ['id','image_url', 'priority']

    def get_image_url(self, obj):
        return get_thumbs(obj.image, self.context.get('request'))



class DepositMediaListSerializer(serializers.ModelSerializer):
    images = serializers.SerializerMethodField()

    class Meta:
        model = DepositMedia
        fields = ['id', 'subject', 'description', 'created_at', 'updated_at', 'images']

        
    def get_images(self, obj):
        top_images = obj.images.order_by('priority')
        return MediaImageSerializer(top_images, many=True, context=self.context).data



class DepositMediaImageSerializer(serializers.ModelSerializer):
    image = serializers.CharField(required=False, allow_null=True, allow_blank=True)
    priority = serializers.IntegerField(required=False, default=0)  # اضافه کردن فیلد priority

    class Meta:
        model = DepositMediaImage
        exclude = ['deposit_media',]

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        request = self.context.get('request', None)
        if request:
            try:
                representation['image'] = absolute_url(request, instance.image.url)
            except Exception as exp:
                pass
        return representation


    def validate_image(self, value):
        if not value:
            return

        file_path = self.extract_file_path(value)
        file_path = f"{file_path}"
        # print(f'-sss-> {file_path}')
        if not os.path.exists(file_path):
            raise serializers.ValidationError("file does not exist, upload again")

        return file_path

    def validate(self, attrs):
        image_data = attrs.pop('image', None)
        if image_data:  # بررسی اینکه اگر image خالی نباشد، پردازش شود
            try:
                # Store the file path for later use in create/update
                attrs['_image_path'] = image_data
            except Exception as e:
                raise serializers.ValidationError(f"Error processing the image file: {str(e)}")

        return attrs
        
    def create(self, validated_data):
        # Handle the image separately
        image_path = validated_data.pop('_image_path', None)
        
        # Create the instance without the image first
        instance = super().create(validated_data)
        
        # If we have an image path, process it after the instance is created
        if image_path:
            try:
                image = import_file(image_path, 'deposit-media-image')
                # Save the Django file directly to the ImageField
                from django.core.files import File
                with open(image_path, 'rb') as f:
                    django_file = File(f)
                    instance.image.save(os.path.basename(image_path), django_file, save=True)
            except Exception as e:
                # Log the error but don't fail the whole creation
                print(f"Error processing image: {str(e)}")
                
        return instance
        
    def update(self, instance, validated_data):
        # Handle the image separately
        image_path = validated_data.pop('_image_path', None)
        
        # Update the instance without the image first
        instance = super().update(instance, validated_data)
        
        # If we have an image path, process it after the instance is updated
        if image_path:
            try:
                # Save the Django file directly to the ImageField
                from django.core.files import File
                with open(image_path, 'rb') as f:
                    django_file = File(f)
                    instance.image.save(os.path.basename(image_path), django_file, save=True)
            except Exception as e:
                # Log the error but don't fail the whole update
                print(f"Error processing image: {str(e)}")
                
        return instance

    def extract_file_path(self, file_path):
        if not file_path:
            return None

        file_path = file_path.replace(self.context['request'].build_absolute_uri('/static'), '')
        static_path = "static"

        return static_path + "/static"+ file_path


class DepositMediaSerializer(serializers.ModelSerializer):
    images = DepositMediaImageSerializer(many=True, required=False)

    class Meta:
        model = DepositMedia
        exclude = ['created_at', 'updated_at', 'deposit']