from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework import status

# ============================================================================
# STRUCTURED SWAGGER DOCUMENTATION FOR DEPOSIT APP
# ============================================================================

# Deposit List Swagger Documentation
deposit_list_swagger = swagger_auto_schema(
    operation_description="Get list of active deposits with filtering and search capabilities",
    operation_summary="List Deposits",
    tags=['Deposit'],
    manual_parameters=[
        openapi.Parameter(
            'search',
            openapi.IN_QUERY,
            description="Search deposits by title. Supports partial matching (case-insensitive).",
            type=openapi.TYPE_STRING,
            required=False,
            example='صندوق'
        ),
        openapi.Parameter(
            'type',
            openapi.IN_QUERY,
            description="Filter deposits by type. Valid values: 'Poll', 'Saving', 'Reporting'",
            type=openapi.TYPE_STRING,
            enum=['Poll', 'Saving', 'Reporting'],
            required=False
        ),
        openapi.Parameter(
            'is_member',
            openapi.IN_QUERY,
            description="Filter deposits to show only those where the current user is a member. Set to 'true' to enable.",
            type=openapi.TYPE_BOOLEAN,
            default='false',
            required=False
        ),
        openapi.Parameter(
            'balance_filter',
            openapi.IN_QUERY,
            description="Filter deposits by balance. Valid values: 'has_balance', 'no_balance'",
            type=openapi.TYPE_STRING,
            enum=['has_balance', 'no_balance'],
            required=False
        ),
        openapi.Parameter(
            'page',
            openapi.IN_QUERY,
            description="Page number for pagination",
            type=openapi.TYPE_INTEGER,
            required=False,
            example=1
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of deposits retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'count': openapi.Schema(type=openapi.TYPE_INTEGER, example=25),
                    'next': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/api/deposits/?page=2'),
                    'previous': openapi.Schema(type=openapi.TYPE_STRING, example=None),
                    'results': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق خانوادگی'),
                                'description': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق برای خانواده'),
                                'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Poll'),
                                'unit_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                                'is_user_member': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                                'members_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                                'balance': openapi.Schema(type=openapi.TYPE_NUMBER, example=1000000.0),
                                'lottery_date': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='2023-05-15'),
                                'created': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                            }
                        )
                    )
                }
            ),
            examples={
                'application/json': {
                    "count": 25,
                    "next": "http://example.com/api/deposits/?page=2",
                    "previous": None,
                    "results": [
                        {
                            "id": 1,
                            "title": "صندوق خانوادگی",
                            "description": "صندوق برای خانواده",
                            "deposit_type": "Poll",
                            "unit_amount": 100000.0,
                            "is_user_member": True,
                            "members_count": 10,
                            "balance": 1000000.0,
                            "lottery_date": "2023-05-15",
                            "created": "2023-01-01T10:00:00Z"
                        }
                    ]
                }
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Detail Swagger Documentation
deposit_detail_swagger = swagger_auto_schema(
    operation_description="Retrieve details of a specific deposit by its ID",
    operation_summary="Get Deposit Details",
    tags=['Deposit'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Deposit details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق خانوادگی'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق برای خانواده'),
                    'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Poll'),
                    'unit_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                    'payment_cycle': openapi.Schema(type=openapi.TYPE_INTEGER, example=30),
                    'max_unit_per_request': openapi.Schema(type=openapi.TYPE_INTEGER, example=5),
                    'max_members_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=20),
                    'is_user_member': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True),
                    'members_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=10),
                    'balance': openapi.Schema(type=openapi.TYPE_NUMBER, example=1000000.0),
                    'total_income': openapi.Schema(type=openapi.TYPE_NUMBER, example=2000000.0),
                    'lottery_date': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='2023-05-15'),
                    'created': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z'),
                    'is_active': openapi.Schema(type=openapi.TYPE_BOOLEAN, example=True)
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "title": "صندوق خانوادگی",
                    "description": "صندوق برای خانواده",
                    "deposit_type": "Poll",
                    "unit_amount": 100000.0,
                    "payment_cycle": 30,
                    "max_unit_per_request": 5,
                    "max_members_count": 20,
                    "is_user_member": True,
                    "members_count": 10,
                    "balance": 1000000.0,
                    "total_income": 2000000.0,
                    "lottery_date": "2023-05-15",
                    "created": "2023-01-01T10:00:00Z",
                    "is_active": True
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Transaction List Swagger Documentation
deposit_transaction_list_swagger = swagger_auto_schema(
    operation_description="Get list of transactions for a specific deposit with time filtering",
    operation_summary="List Deposit Transactions",
    tags=['Deposit'],
    manual_parameters=[
        openapi.Parameter(
            'filter',
            openapi.IN_QUERY,
            description="Filter transactions by time period. Valid values: 'yesterday', 'last_week', 'last_month', 'last_three_months'",
            type=openapi.TYPE_STRING,
            enum=['yesterday', 'last_week', 'last_month', 'last_three_months'],
            required=False
        )
    ],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="List of deposit transactions retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                        'transaction_type': openapi.Schema(type=openapi.TYPE_STRING, example='deposit'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='Monthly payment'),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z'),
                        'user': openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                'fullname': openapi.Schema(type=openapi.TYPE_STRING, example='John Doe'),
                                'phone_number': openapi.Schema(type=openapi.TYPE_STRING, example='+989012345678')
                            }
                        )
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "amount": 100000.0,
                        "transaction_type": "deposit",
                        "description": "Monthly payment",
                        "created_at": "2023-01-01T10:00:00Z",
                        "user": {
                            "id": 1,
                            "fullname": "John Doe",
                            "phone_number": "+989012345678"
                        }
                    }
                ]
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'message': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Deposit not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Member Detail Swagger Documentation
deposit_member_detail_swagger = swagger_auto_schema(
    operation_description="Get detailed information about a specific member of a deposit including user name, phone number, unpaid installments count, and total debt amount",
    operation_summary="Get Deposit Member Details",
    tags=['Deposit'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Member details retrieved successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'user_name': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description="User's full name",
                        example='John Doe'
                    ),
                    'phone_number': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        description="User's phone number",
                        example='+989012345678'
                    ),
                    'unpaid_installments_count': openapi.Schema(
                        type=openapi.TYPE_INTEGER,
                        description="Number of unpaid installments",
                        example=2
                    ),
                    'total_debt_amount': openapi.Schema(
                        type=openapi.TYPE_NUMBER,
                        description="Total debt amount",
                        example=200000.0
                    )
                }
            ),
            examples={
                'application/json': {
                    "user_name": "John Doe",
                    "phone_number": "+989012345678",
                    "unpaid_installments_count": 2,
                    "total_debt_amount": 200000.0
                }
            }
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Member or deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)



# User Deposits and Requests Swagger Documentation
user_deposits_requests_swagger = swagger_auto_schema(
    operation_description="Get a combined list of user's deposits, join requests, and create deposit requests",
    operation_summary="Get User Deposits and Requests",
    tags=['Deposit'],
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Combined list of user's deposits and requests",
            schema=openapi.Schema(
                type=openapi.TYPE_ARRAY,
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                        'title': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق خانوادگی'),
                        'deposit_type': openapi.Schema(type=openapi.TYPE_STRING, example='Poll'),
                        'description': openapi.Schema(type=openapi.TYPE_STRING, example='صندوق برای خانواده'),
                        'unit_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=100000.0),
                        'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z'),
                        'item_type': openapi.Schema(type=openapi.TYPE_STRING, example='deposit'),
                        'sort_order': openapi.Schema(type=openapi.TYPE_INTEGER, example=2),
                        'status': openapi.Schema(type=openapi.TYPE_STRING, example='approved'),
                        'rejection_reason': openapi.Schema(type=openapi.TYPE_STRING, example=None),
                        'balance': openapi.Schema(type=openapi.TYPE_NUMBER, example=1000000.0),
                        'total_income': openapi.Schema(type=openapi.TYPE_NUMBER, example=2000000.0),
                        'requested_unit_count': openapi.Schema(type=openapi.TYPE_INTEGER, example=None),
                        'monthly_installment_amount': openapi.Schema(type=openapi.TYPE_NUMBER, example=None),
                        'total_withdrawals': openapi.Schema(type=openapi.TYPE_NUMBER, example=500000.0),
                        'due_date': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATE, example='2023-05-15')
                    }
                )
            ),
            examples={
                'application/json': [
                    {
                        "id": 1,
                        "title": "صندوق خانوادگی",
                        "deposit_type": "Poll",
                        "description": "صندوق برای خانواده",
                        "unit_amount": 100000.0,
                        "created_at": "2023-01-01T10:00:00Z",
                        "item_type": "deposit",
                        "sort_order": 2,
                        "status": "approved",
                        "rejection_reason": None,
                        "balance": 1000000.0,
                        "total_income": 2000000.0,
                        "requested_unit_count": None,
                        "monthly_installment_amount": None,
                        "total_withdrawals": 500000.0,
                        "due_date": "2023-05-15"
                    }
                ]
            }
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Media Create Swagger Documentation
deposit_media_create_swagger = swagger_auto_schema(
    operation_description="Create a new media for a deposit with associated images. Each media can contain one or more images with priority settings.",
    operation_summary="Create Deposit Media",
    tags=['Deposit'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        required=['title'],
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Media title',
                example='Deposit Photos'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Media description',
                example='Photos of the deposit activities'
            ),
            'images': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                description='Array of images to associate with this media',
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'image': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            format=openapi.FORMAT_BINARY,
                            description='Image file'
                        ),
                        'priority': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Image priority (lower number = higher priority)',
                            example=1
                        )
                    }
                )
            )
        }
    ),
    responses={
        status.HTTP_201_CREATED: openapi.Response(
            description="Media created successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='Deposit Photos'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='Photos of the deposit activities'),
                    'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                    'images': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                'image': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/media/images/photo.jpg'),
                                'priority': openapi.Schema(type=openapi.TYPE_INTEGER, example=1)
                            }
                        )
                    ),
                    'created_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-01T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "title": "Deposit Photos",
                    "description": "Photos of the deposit activities",
                    "deposit": 123,
                    "images": [
                        {
                            "id": 1,
                            "image": "http://example.com/media/images/photo.jpg",
                            "priority": 1
                        }
                    ],
                    "created_at": "2023-01-01T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'title': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(type=openapi.TYPE_STRING),
                        example=['This field is required.']
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Deposit not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Deposit not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)

# Deposit Media Update Swagger Documentation
deposit_media_update_swagger = swagger_auto_schema(
    operation_description="Update an existing deposit media and its associated images. Can add new images, update existing ones, or delete images by setting image field to 'delete'.",
    operation_summary="Update Deposit Media",
    tags=['Deposit'],
    request_body=openapi.Schema(
        type=openapi.TYPE_OBJECT,
        properties={
            'title': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Updated media title',
                example='Updated Deposit Photos'
            ),
            'description': openapi.Schema(
                type=openapi.TYPE_STRING,
                description='Updated media description',
                example='Updated photos of the deposit activities'
            ),
            'images': openapi.Schema(
                type=openapi.TYPE_ARRAY,
                description='Array of images to update/add/delete',
                items=openapi.Schema(
                    type=openapi.TYPE_OBJECT,
                    properties={
                        'id': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Image ID for updating existing image (optional for new images)',
                            example=1
                        ),
                        'image': openapi.Schema(
                            type=openapi.TYPE_STRING,
                            description='Image file or "delete" to remove existing image',
                            example='new_image.jpg'
                        ),
                        'priority': openapi.Schema(
                            type=openapi.TYPE_INTEGER,
                            description='Image priority (lower number = higher priority)',
                            example=1
                        )
                    }
                )
            )
        }
    ),
    responses={
        status.HTTP_200_OK: openapi.Response(
            description="Media updated successfully",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                    'title': openapi.Schema(type=openapi.TYPE_STRING, example='Updated Deposit Photos'),
                    'description': openapi.Schema(type=openapi.TYPE_STRING, example='Updated photos of the deposit activities'),
                    'deposit': openapi.Schema(type=openapi.TYPE_INTEGER, example=123),
                    'images': openapi.Schema(
                        type=openapi.TYPE_ARRAY,
                        items=openapi.Schema(
                            type=openapi.TYPE_OBJECT,
                            properties={
                                'id': openapi.Schema(type=openapi.TYPE_INTEGER, example=1),
                                'image': openapi.Schema(type=openapi.TYPE_STRING, example='http://example.com/media/images/updated_photo.jpg'),
                                'priority': openapi.Schema(type=openapi.TYPE_INTEGER, example=1)
                            }
                        )
                    ),
                    'updated_at': openapi.Schema(type=openapi.TYPE_STRING, format=openapi.FORMAT_DATETIME, example='2023-01-02T10:00:00Z')
                }
            ),
            examples={
                'application/json': {
                    "id": 1,
                    "title": "Updated Deposit Photos",
                    "description": "Updated photos of the deposit activities",
                    "deposit": 123,
                    "images": [
                        {
                            "id": 1,
                            "image": "http://example.com/media/images/updated_photo.jpg",
                            "priority": 1
                        }
                    ],
                    "updated_at": "2023-01-02T10:00:00Z"
                }
            }
        ),
        status.HTTP_400_BAD_REQUEST: openapi.Response(
            description="Validation error or processing error",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Error updating the deposit media'
                    ),
                    'images_error': openapi.Schema(
                        type=openapi.TYPE_OBJECT,
                        example={'image': ['This field is required.']}
                    )
                }
            )
        ),
        status.HTTP_404_NOT_FOUND: openapi.Response(
            description="Media or image not found",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Media not found.'
                    )
                }
            )
        ),
        status.HTTP_401_UNAUTHORIZED: openapi.Response(
            description="Authentication required",
            schema=openapi.Schema(
                type=openapi.TYPE_OBJECT,
                properties={
                    'detail': openapi.Schema(
                        type=openapi.TYPE_STRING,
                        example='Authentication credentials were not provided.'
                    )
                }
            )
        )
    }
)
