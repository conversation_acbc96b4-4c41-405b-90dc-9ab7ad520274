from rest_framework.generics import ListAPIView, GenericAPIView
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi

from .models import Region, UserRegion, InvitationLink
from .serializers import RegionListSerializer, ChangeUserRegionSerializer, UserRegionSerializer, InvitationLinkSerializer, UserRegionDetailSerializer
from .docs import region_list_swagger, change_user_region_swagger, user_invitation_links_list_swagger, create_invitation_link_swagger, user_region_detail_swagger


class RegionListAPIView(ListAPIView):
    """
    API view to list all available regions with user membership status
    """
    queryset = Region.objects.all().order_by('name')
    serializer_class = RegionListSerializer
    permission_classes = [IsAuthenticated]

    @region_list_swagger
    def get(self, request, *args, **kwargs):
        """Get list of all regions with user membership status"""
        return super().get(request, *args, **kwargs)


class ChangeUserRegionAPIView(GenericAPIView):
    """
    API view to change user's region membership
    """
    serializer_class = ChangeUserRegionSerializer
    permission_classes = [IsAuthenticated]

    @change_user_region_swagger
    def post(self, request, *args, **kwargs):
        """Change user's region membership"""
        serializer = self.get_serializer(data=request.data)
        if not serializer.is_valid():
            return Response({
                'status': 'error',
                'message': 'Validation failed',
                'errors': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

        region_id = serializer.validated_data['region_id']
        user = request.user

        # Check if region exists
        try:
            region = Region.objects.get(id=region_id)
        except Region.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'Region not found'
            }, status=status.HTTP_404_NOT_FOUND)

        # Check if user already has a region membership
        try:
            user_region = UserRegion.objects.get(user=user)
            # Update existing membership
            user_region.region = region
            user_region.is_active = True
            user_region.save()
            message = "Region changed successfully"
        except UserRegion.DoesNotExist:
            # Create new membership
            user_region = UserRegion.objects.create(
                user=user,
                region=region,
                is_active=True
            )
            message = "Region membership created successfully"

        return Response({
            'status': 'success',
            'message': message,
        }, status=status.HTTP_200_OK)


class UserInvitationLinksListAPIView(ListAPIView):
    """
    API view to list all invitation links created by the current user
    """
    serializer_class = InvitationLinkSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """Get invitation links for the current user's region"""
        user = self.request.user
        try:
            user_region = user.region_membership
            return InvitationLink.objects.filter(user_region=user_region)
        except:
            return InvitationLink.objects.none()

    @user_invitation_links_list_swagger
    def get(self, request, *args, **kwargs):
        """Get list of user's invitation links with usage status"""
        return super().get(request, *args, **kwargs)


class CreateInvitationLinkAPIView(GenericAPIView):
    """
    API view to create a new invitation link for the current user
    """
    permission_classes = [IsAuthenticated]

    @create_invitation_link_swagger
    def post(self, request, *args, **kwargs):
        """Create a new invitation link for the user's region"""
        # Check if user has a region membership
        try:
            user_region = request.user.region_membership
        except:
            return Response({
                'status': 'error',
                'message': 'User must be a member of a region to create invitation links.'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create the invitation link directly
        invitation_link = InvitationLink.objects.create(
            user_region=user_region,
            invitation_code=request.user.generate_ref_link()
        )

        # Return the created invitation link data
        response_serializer = InvitationLinkSerializer(invitation_link)
        return Response({
            'status': 'success',
            'message': 'Invitation link created successfully.',
            'invitation_link': response_serializer.data
        }, status=status.HTTP_201_CREATED)


class UserRegionDetailAPIView(GenericAPIView):
    """
    API view to get current user's region details as an object
    """
    serializer_class = UserRegionDetailSerializer
    permission_classes = [IsAuthenticated]

    @user_region_detail_swagger
    def get(self, request, *args, **kwargs):
        """Get current user's region details"""
        user = request.user
        
        # Check if user has a region membership
        try:
            user_region = user.region_membership
            region = user_region.region
        except UserRegion.DoesNotExist:
            return Response({
                'status': 'error',
                'message': 'User is not a member of any region'
            }, status=status.HTTP_404_NOT_FOUND)

        # Serialize and return the user's region data
        serializer = self.get_serializer(region)
        return Response(serializer.data, status=status.HTTP_200_OK)
