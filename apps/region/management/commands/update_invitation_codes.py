from django.core.management.base import BaseCommand
from django.db import transaction
from apps.region.models import UserRegion
from apps.account.models import User


class Command(BaseCommand):
    help = 'Updates all existing invitation codes in UserRegion to follow the new format HAMJEEB-XXXXXXX'

    def handle(self, *args, **options):
        # Get all UserRegion objects that have an invitation code
        user_regions = UserRegion.objects.filter(invitation_code__isnull=False)
        total_count = user_regions.count()
        
        self.stdout.write(self.style.SUCCESS(f'Found {total_count} UserRegion records with invitation codes to update'))
        
        updated_count = 0
        skipped_count = 0
        
        # Process each UserRegion
        with transaction.atomic():
            for user_region in user_regions:
                try:
                    # Generate a new invitation code using the user's generate_ref_link method
                    new_code = user_region.user.generate_ref_link()
                    
                    # Update the invitation code
                    old_code = user_region.invitation_code
                    user_region.invitation_code = new_code
                    user_region.save(update_fields=['invitation_code'])
                    
                    updated_count += 1
                    self.stdout.write(f'Updated invitation code for UserRegion ID {user_region.id}: {old_code} -> {new_code}')
                    
                except Exception as e:
                    skipped_count += 1
                    self.stdout.write(self.style.ERROR(f'Error updating UserRegion ID {user_region.id}: {str(e)}'))
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} invitation codes'))
        if skipped_count > 0:
            self.stdout.write(self.style.WARNING(f'Skipped {skipped_count} records due to errors'))