from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from ajaxdatatable.admin import AjaxDatatable

from unfold.admin import ModelAdmin
from unfold.decorators import display, action

from apps.region.models import Region, UserRegion, InvitationLink
from utils.admin import project_admin_site


@admin.register(Region)
class RegionAdmin(ModelAdmin):
    list_display = ('name', 'invitation_code', 'members_count', 'created_at',)
    search_fields = ('name', 'invitation_code')
    list_filter = ('created_at', 'updated_at')
    
    fieldsets = (
        (None, {
            "fields": ("name", "description", "invitation_code"),
        }),
        (_("Timestamps"), {
            "fields": ("created_at", "updated_at"),
            "classes": ["tab"],
        }),
    )
    
    readonly_fields = ('created_at', 'updated_at')
    
    @display(description=_("Members Count"))
    def members_count(self, obj):
        # Create a link to the User admin filtered by this region
        url = reverse('admin:account_user_changelist') + f'?region_membership__region={obj.id}'
        count = obj.members.filter(is_active=True).count()
        return format_html(
            '<a href="{}"><i class="material-icons" style="font-size: 16px;">{} </i></a>',
            url, count
        )


# We don't register UserRegion with admin.site as requested
# Instead, we'll use the User admin to manage region memberships


@admin.register(InvitationLink)
class InvitationLinkAdmin(ModelAdmin):
    list_display = ('invitation_code', 'user_region_info', 'is_used', 'used_by_info', 'created_at', 'used_at')
    search_fields = ('invitation_code', 'user_region__user__fullname', 'used_by__fullname')
    list_filter = ('is_used', 'created_at', 'used_at')

    fieldsets = (
        (None, {
            "fields": ("user_region", "invitation_code", "is_used", "used_by", "used_at"),
        }),
        (_("Timestamps"), {
            "fields": ("created_at",),
            "classes": ["tab"],
        }),
    )

    readonly_fields = ('created_at', 'used_at')

    @display(description=_("User Region"))
    def user_region_info(self, obj):
        if obj.user_region:
            url = reverse('admin:account_user_change', args=[obj.user_region.user.id])
            return format_html(
                '<a href="{}">{} ({})</a>',
                url, obj.user_region.user.fullname, obj.user_region.region.name
            )
        return "-"

    @display(description=_("Used By"))
    def used_by_info(self, obj):
        if obj.used_by:
            url = reverse('admin:account_user_change', args=[obj.used_by.id])
            return format_html(
                '<a href="{}">{}</a>',
                url, obj.used_by.fullname
            )
        return "-"


# Register with project_admin_site
project_admin_site.register(Region, RegionAdmin)
project_admin_site.register(InvitationLink, InvitationLinkAdmin)

# We don't register UserRegion with project_admin_site as requested
# Instead, we'll use the User admin to manage region memberships