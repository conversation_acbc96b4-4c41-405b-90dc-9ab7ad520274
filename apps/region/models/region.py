from django.db import models
import random
import string

from apps.account.models import User


class Region(models.Model):
    name = models.CharField(max_length=255)
    description = models.TextField(null=True, blank=True)  
    invitation_code = models.CharField(max_length=255, unique=True, blank=True, null=True)
    
    created_at = models.DateTimeField(auto_now_add=True) 
    updated_at = models.DateTimeField(auto_now=True)  
    
    def __str__(self):
        return self.name 
    
    def generate_invitation_code(self):
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))        
        return f"REGION-{random_chars}"
    
    def save(self, *args, **kwargs):
        if not self.invitation_code:
            self.invitation_code = self.generate_invitation_code()
        super().save(*args, **kwargs)
        
    @property
    def members_count(self):
        """Return the number of members in this region"""
        return self.members.count() 


class UserRegion(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='region_membership')
    region = models.ForeignKey(Region, on_delete=models.CASCADE, related_name='members')
    invitation_code = models.CharField(max_length=255, unique=True, blank=True, null=True)
    invited_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='invited_users')
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True) 
    
    def __str__(self):
        return f"{self.user.fullname} in {self.region.name}"

    def save(self, *args, **kwargs):
        if not self.invitation_code:  
            self.invitation_code = self.user.generate_ref_link()
        super().save(*args, **kwargs)
        
    @property
    def invited_users_count(self):
        """Return the number of users who joined using this user's invitation code"""
        from apps.region.models import UserRegion
        return UserRegion.objects.filter(invited_by=self.user).count()


class InvitationLink(models.Model):
    """
    Model to store multiple invitation links for each UserRegion
    Each UserRegion can have multiple invitation links with different statuses
    """
    user_region = models.ForeignKey(UserRegion, on_delete=models.CASCADE, related_name='invitation_links')
    invitation_code = models.CharField(max_length=255, unique=True)
    is_used = models.BooleanField(default=False)
    used_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='used_invitation_links')
    created_at = models.DateTimeField(auto_now_add=True)
    used_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'Invitation Link'
        verbose_name_plural = 'Invitation Links'

    def __str__(self):
        status = "Used" if self.is_used else "Active"
        return f"{self.user_region.user.fullname} - {self.invitation_code} ({status})"

    def save(self, *args, **kwargs):
        # invitation_code should be provided when creating the instance
        super().save(*args, **kwargs)

    def generate_invitation_code(self):
        """Generate a unique invitation code"""
        random_chars = ''.join(random.choices(string.ascii_uppercase + string.digits, k=7))
        return f"HAMJEEB-{random_chars}"

    def mark_as_used(self, used_by_user):
        """Mark this invitation link as used by a specific user"""
        from django.utils import timezone
        self.is_used = True
        self.used_by = used_by_user
        self.used_at = timezone.now()
        self.save()



