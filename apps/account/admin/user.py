from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.translation import gettext_lazy as _
from rest_framework.authtoken.models import TokenProxy
from ajaxdatatable.admin import AjaxDatatable
from django.contrib import admin
from django.templatetags.static import static
from django.db.models import Q

from django import forms
from django.urls import path, reverse
from django.shortcuts import render, redirect
from django.contrib import messages

from unfold.admin import ModelAdmin, TabularInline, StackedInline
from unfold.forms import AdminPasswordChangeForm, UserChangeForm, UserCreationForm

from unfold.contrib.filters.admin import (
    RangeNumericFilter, 
    RangeDateFilter, 
    BooleanRadioFilter,
    ChoicesRadioFilter
)
from unfold.decorators import display, action
from unfold.widgets import UnfoldAdminTextInputWidget
from apps.account.models import User, AdminUser, GuestAdminUser, RegionOwnerUser, LoginHistory
from utils.admin import project_admin_site
from utils.date_utils import gregorian_to_jalali, format_jalali_date
from apps.region.models.region import UserRegion, Region


class CustomUserCreationForm(UserCreationForm):
    invitation_code = forms.CharField(
        required=False,
        help_text=_("Optional. Enter an invitation code to join a region."),
        label=_("Invitation Code"),
        widget=UnfoldAdminTextInputWidget(),  
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add help text to phone_number field
        if 'phone_number' in self.fields:
            self.fields['phone_number'].help_text = _("Phone number should be in format: +98 901 2045375")
    
    def clean_invitation_code(self):
        invitation_code = self.cleaned_data.get('invitation_code')
        
        if not invitation_code:
            return invitation_code
            
        # Clean the invitation code if it contains a URL path
        if '/' in invitation_code:
            invitation_code = invitation_code.split('/')[-1]
            
        # Validate the invitation code
        from apps.region.models.region import UserRegion, Region
        
        # First try to find a UserRegion with this invitation code
        user_region = UserRegion.objects.filter(invitation_code=invitation_code).first()
        if user_region:
            return invitation_code
            
        # If not found in UserRegion, try to find a Region with this invitation code
        region = Region.objects.filter(invitation_code=invitation_code).first()
        if region:
            return invitation_code
            
        # If not found in either, the code is invalid
        raise forms.ValidationError(_("Invalid invitation code."))
    
    def save(self, commit=True):
        user = super().save(commit=commit)
        
        invitation_code = self.cleaned_data.get('invitation_code')
        if invitation_code and commit:
            from apps.region.models.region import UserRegion, Region
            
            # First try to find a UserRegion with this invitation code
            try:
                invitation_user_region = UserRegion.objects.get(invitation_code=invitation_code)
                # If found, use the UserRegion's region and set the inviter
                region = invitation_user_region.region
                invited_by = invitation_user_region.user
            except UserRegion.DoesNotExist:
                # If not found in UserRegion, try to find a Region with this invitation code
                try:
                    region = Region.objects.get(invitation_code=invitation_code)
                    invited_by = None  # No specific user invited this person
                except Region.DoesNotExist:
                    # This shouldn't happen due to validation, but just in case
                    return user
            
            # Create a new UserRegion for this user
            UserRegion.objects.create(
                user=user,
                region=region,
                invited_by=invited_by,
                is_active=False
            )
            
        return user



class LoginHistoryAdmin(TabularInline):
    model = LoginHistory
    extra = 0
    readonly_fields = ('jalali_last_login_at',)
    ordering = ('-id',)
    hide_title = True  # Unfold specific option to hide  title row

    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj):
        return False

    def has_change_permission(self, request, obj=None):
        return False
        
    @display(description=_('Last Login At'))
    def jalali_last_login_at(self, obj):
        if obj.last_login_at:
            return gregorian_to_jalali(obj.last_login_at, "%Y/%m/%d %H:%M")
        return "-"


class UserRegionInline(StackedInline):
    model = UserRegion
    extra = 1
    can_delete = False
    fk_name = 'user'  # مشخص می‌کنیم که از فیلد 'user' برای ارتباط استفاده شود
    fields = ('region', 'invitation_code', 'invited_by', 'is_active')
    readonly_fields = ('invitation_code',)
    
    def has_add_permission(self, request, obj=None):
        # Allow adding only if the user doesn't already have a UserRegion
        if obj and hasattr(obj, 'region_membership') and obj.region_membership:
            return False
        return True


class BaseUserAdminMixin(BaseUserAdmin, ModelAdmin):
    """Base mixin for user admin classes with common functionality"""
    form = UserChangeForm
    add_form = CustomUserCreationForm
    change_password_form = AdminPasswordChangeForm
    list_display = (
        'user_info', 'user_status', 'jalali_last_login', 'jalali_date_joined', 'ref_link'
    )
    list_filter = (
        ('is_staff', BooleanRadioFilter),
        ('is_active', BooleanRadioFilter),
        ('date_joined', RangeDateFilter),
        ('last_login', RangeDateFilter),
        'region_membership__region',
    )
    ordering = ('-id',)
    readonly_fields = ('jalali_date_joined', 'jalali_last_login', 'display_auth_token')
    search_fields = (
        'phone_number', 'email', 'fullname', 'id',
    )
    compressed_fields = False    
    list_filter_submit = True  # Enable submit button for filters
    radio_fields = {
        "gender": admin.HORIZONTAL,
    }
    
    def delete_model(self, request, obj):
        obj.soft_delete()
        messages.success(request, _("User has been soft deleted successfully."))
    
    def delete_queryset(self, request, queryset):
        for obj in queryset:
            obj.soft_delete()
        messages.success(request, _("Selected users have been soft deleted successfully."))
        
    def has_delete_permission(self, request, obj=None):
        return True
        
    def get_deleted_objects(self, objs, request):
        return [], 0, set(), []
        
    def delete_view(self, request, object_id, extra_context=None):
        obj = self.get_object(request, object_id)
        if obj is None:
            return self._get_obj_does_not_exist_redirect(request, self.model._meta, object_id)
            
        if request.method == 'POST':
            obj.soft_delete()
            messages.success(request, _("User has been soft deleted successfully."))
            return redirect(reverse(f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist'))
            
        # اگر متد درخواست POST نباشد، صفحه تأیید حذف را نمایش می‌دهیم
        context = {
            'title': _('Are you sure?'),
            'object_name': str(obj),
            'object': obj,
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
            **self.admin_site.each_context(request),
            **(extra_context or {}),
        }
        return render(request, 'admin/delete_confirmation.html', context)
        
    def changelist_view(self, request, extra_context=None):
        if request.method == 'POST' and 'action' in request.POST and request.POST['action'] == 'delete_selected':
            # دریافت شناسه‌های کاربران انتخاب شده
            selected = request.POST.getlist('_selected_action')
            if selected:
                # دریافت کوئری‌ست کاربران انتخاب شده
                queryset = self.get_queryset(request).filter(pk__in=selected)
                # اعمال soft delete برای هر کاربر
                for obj in queryset:
                    obj.soft_delete()
                messages.success(request, _("Selected users have been soft deleted successfully."))
                # بازگشت به صفحه لیست کاربران
                return redirect(reverse(f'admin:{self.model._meta.app_label}_{self.model._meta.model_name}_changelist'))
                
        # اگر عملیات حذف گروهی نباشد، رفتار پیش‌فرض را انجام می‌دهیم
        return super().changelist_view(request, extra_context)
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ( ('fullname', 'phone_number'), 'email', 'invitation_code', 'birthdate', 'gender','avatar', 'shba_number'),
        }),
        (_('Password'), {
            'fields': ('password1', 'password2'),
            'classes': ('collapse',),
        }),
        (_('Permissions'), {
            'fields': ('is_active', 'is_staff', ),
            'classes': ('collapse',),
        }),
    )
    fieldsets = (
        (None, {"fields": (("fullname", "email"),)}),
        (
            _("Basic Information"),
            {
                "fields": ("gender", "avatar", "phone_number", "birthdate", 'shba_number', 'password'),
                "classes": ["tab"],
            },
        ),
        (
            _('Country & City'), {
                'fields': ('city',),
                "classes": ["tab"],
            }
        ),
        (
            _('Device Information'), {
                'fields': ('device_id', 'fcm', 'language', ),
                "classes": ["tab"],
            }
        ),
        (
            _('Authentication'), {
                'fields': ('display_auth_token', 'is_active', 'is_staff', 'groups'),
                "classes": ["tab"],
            }
        ),
        (
            _('Region Information'), {
                'fields': (),  # خالی گذاشتن فیلدها
                "classes": ["tab"],
                "description": _("User's region membership information. You can add or edit region membership in the section below."),
            }
        ),
        (
            _('Important dates'), {
                'fields': ('jalali_last_login', 'jalali_date_joined', 'deleted_at'),
                "classes": ["tab"],
            }
        ),
    )

    inlines = [UserRegionInline, LoginHistoryAdmin]
            
    @display(description=_("Authentication Token"))
    def display_auth_token(self, instance: User):
        from rest_framework.authtoken.models import Token
        from django.utils.html import format_html
        
        try:
            token, created = Token.objects.get_or_create(user=instance)
            return format_html('<code style="word-break: break-all;">{}</code>', token.key)
        except Exception as e:
            return format_html('<span class="error">{}</span>', str(e))
        
    @display(description='User Information', header=True)
    def user_info(self, obj):
        avatar_path = obj.avatar.url if obj.avatar else static("images/user1.svg")
        
        return [
            obj.fullname,
            str(obj.phone_number),
            obj.get_initials(),  
            {
                "path": avatar_path,
                "height": 33,
                "width": 38,
                "borderless": True,
                "squared": True,
            },  
        ]

    @display(description='Status', label={
        True: 'success',   # Active users in green
        False: 'danger',   # Inactive users in red
    })
    def user_status(self, obj):
        return obj.is_active, _('Active') if obj.is_active else _('Inactive')

    @display(description='Referral Code')
    def ref_link(self, obj):
        try:
            if hasattr(obj, 'region_membership'):
                region_membership = obj.region_membership
                if region_membership:
                    return f"https://hammjeeb.ir/link/{region_membership.invitation_code}"
            return None
        except Exception as exp:
            print(f'--> {exp}')
            return None
            
    @display(description=_('Date Joined'))
    def jalali_date_joined(self, obj):
        if obj.date_joined:
            # Use gregorian_to_jalali to get numeric format
            return gregorian_to_jalali(obj.date_joined, "%Y/%m/%d %H:%M")
        return "-"
        
    @display(description=_('Last Login'))
    def jalali_last_login(self, obj):
        if obj.last_login:
            # Use gregorian_to_jalali to get numeric format
            return gregorian_to_jalali(obj.last_login, "%Y/%m/%d %H:%M")
        return "-"
        
    @admin.display(description=_("Region Information"))
    def display_region_info(self, instance: User):
        from django.utils.html import format_html
        
        try:
            if hasattr(instance, 'region_membership') and instance.region_membership:
                user_region = instance.region_membership
                region_name = user_region.region.name if user_region.region else "-"
                invitation_code = user_region.invitation_code or "-"
                is_active = _("Active") if user_region.is_active else _("Inactive")
                invited_by = user_region.invited_by.fullname if user_region.invited_by else "-"
                
                return format_html(
                    '<div class="region-info">'
                    '<p><strong>{}: </strong>{}</p>'
                    '<p><strong>{}: </strong>{}</p>'
                    '<p><strong>{}: </strong>{}</p>'
                    '<p><strong>{}: </strong>{}</p>'
                    '</div>',
                    _("Region"), region_name,
                    _("Invitation Code"), invitation_code,
                    _("Status"), is_active,
                    _("Invited By"), invited_by
                )
            else:
                return format_html(
                    '<p>{}</p>'
                    '<p>{}</p>',
                    _("No region membership found."),
                    _("You can add a region membership in the section below.")
                )
        except Exception as e:
            return format_html('<span class="error">{}</span>', str(e))
    
    # def get_form(self, request, obj=None, change=False, **kwargs):
    #     form = super().get_form(request, obj, change, **kwargs)
    #     form.base_fields["invitation_code"].widget = UnfoldAdminTextInputWidget()
    #     return form
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.filter(is_deleted=False)

class UserAdmin(BaseUserAdminMixin):
    """Admin for users with active region membership"""
    
    def get_queryset(self, request):
        """Filter users to only show those with active region membership"""
        queryset = super().get_queryset(request)
        return queryset.filter(
            region_membership__isnull=False,
            region_membership__is_active=True
        )


class GuestUserAdmin(BaseUserAdminMixin):
    """Admin for users without region membership or with inactive region membership"""
    
    def get_queryset(self, request):
        """Filter users to only show those without region membership or with inactive region membership"""
        queryset = super().get_queryset(request)
        return queryset.filter(
            Q(region_membership__isnull=True) | 
            Q(region_membership__is_active=False)
        )


class AdminUserAdmin(BaseUserAdminMixin):
    """Admin for staff or superuser users"""
    
    def get_queryset(self, request):
        """Filter users to only show those with is_staff or is_superuser active"""
        queryset = super().get_queryset(request)
        return queryset.filter(
            Q(is_staff=True) | Q(is_superuser=True) | Q(is_admin=True)
        )


class RegionOwnerUserAdmin(BaseUserAdminMixin):
    """Admin for region owner users"""
    
    def get_queryset(self, request):
        """Filter users to only show those who are region owners"""
        queryset = super().get_queryset(request)
        return queryset.filter(regions__isnull=False)
    
    @display(description='Regions')
    def user_regions(self, obj):
        regions = obj.regions.all()
        if regions:
            return ", ".join([region.name for region in regions])
        return "-"
    
    list_display = (
        'user_info', 'user_status', 'user_regions', 'jalali_last_login', 'jalali_date_joined'
    )




# Register models with the project_admin_site
project_admin_site.register(User, UserAdmin)
project_admin_site.register(GuestAdminUser, GuestUserAdmin)
project_admin_site.register(AdminUser, AdminUserAdmin)
project_admin_site.register(RegionOwnerUser, RegionOwnerUserAdmin)

# Register with default admin site
admin.site.unregister(TokenProxy)
admin.site.register(User, UserAdmin)
admin.site.register(GuestAdminUser, GuestUserAdmin)
admin.site.register(AdminUser, AdminUserAdmin)
admin.site.register(RegionOwnerUser, RegionOwnerUserAdmin)
