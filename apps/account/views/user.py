import logging
import requests
import json
from rest_framework.generics import CreateAPIView, RetrieveUpdateAPIView, GenericAPIView, RetrieveAPIView, UpdateAPIView, ListAPIView
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.exceptions import AuthenticationFailed
from django.utils.translation import gettext_lazy as _
from django.shortcuts import get_object_or_404
from rest_framework.authtoken.models import Token

from django.utils import timezone
from rest_framework.authentication import TokenAuthentication
from django.contrib.auth import authenticate
from phonenumbers import parse, region_code_for_number
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from rest_framework.exceptions import ValidationError

from utils.exceptions import InvaliedCodeVrify, ExpiredCodeException, ServiceUnavailableException
from apps.account.models import User
from apps.account.serializers import UserRegisterSerializer, UserProfileSerializer, UserVerifySerializer, UserLoginSerializer, UserRecoverPasswordSerializer, UserResetPasswordSerializer,UserResetPasswordSerializer, UserFCMSerializer
from apps.account.tasks import send_otp_code_sms, send_otp_code_whatsapp, send_otp_code_kavenegar
from apps.account.permissions import IsActiveUser 
from apps.account.doc import (
    user_register_swagger, user_verify_swagger, user_login_swagger,
    user_recover_password_swagger, user_reset_password_swagger,
    user_profile_swagger, user_update_swagger, user_delete_swagger,
    update_fcm_swagger
)
from apps.region.models import UserRegion
from utils.exceptions import AppAPIException
from utils.redis import RedisManager
from config.settings import base as settings 

logger = logging.getLogger(__name__)




       
class UserRegisterView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserRegisterSerializer
    
    
    @user_register_swagger
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        if User.objects.filter(phone_number=request.data.get('phone_number')).exists():
            raise ValidationError({"phone_number": "This phone number is already registered."})

        serializer.is_valid(raise_exception=True)
        data = serializer.data
        
        code = RedisManager.generate_otp_code()
        logger.info(f"phone= {data['phone_number']}")
        print(f' send {code}/{data["phone_number"]}')
        phone_number = RedisManager().add_to_redis(code, **data)


        phone = data['phone_number']
        
        # ارسال کد تایید با کاونگار
        send_otp_code_kavenegar(phone, code)
        # برای ارسال غیرهمزمان (با Celery) می‌توانید از خط زیر استفاده کنید
        # send_otp_code_kavenegar.delay(phone, code)
        
        # ارسال کد تایید با واتساپ
        send_otp_code_whatsapp.delay(data['phone_number'], code, data['fullname'])
        
        password = data.pop('password')
        return Response(
            data= {
                "user": data,
                "message": "The otp code was sent to the user's phone"
            },
            status=status.HTTP_202_ACCEPTED,
        )
    
    
    
class UserVerifyView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserVerifySerializer
    
    @user_verify_swagger
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        try:
            verify_data = RedisManager().get_by_redis(data['phone_number'])
            if not verify_data:
                raise ValidationError({"code": "Verification data not found or expired."})
                # raise ExpiredCodeException("Verification data not found or expired.")
        except (ServiceUnavailableException) as e:
            raise AppAPIException({"message": str(e)}, status_code=e.status_code)
        except ExpiredCodeException:
            # raise ExpiredCodeException("The verification code has expired.")
            raise ValidationError({"code": "The verification code has expired."})


        code = self.valied_code(data['code'], verify_data['code'])
        del verify_data['code']

        # Add name and family from request data if provided
        if 'name' in data:
            verify_data['name'] = data['name']
        if 'family' in data:
            verify_data['family'] = data['family']

        user = self.perform_create(
            phone_number=serializer.data['phone_number'], **verify_data
        )
        # Token.objects.filter(user=user).delete()
        token, _ = Token.objects.get_or_create(user=user)
        has_user_region = hasattr(user, 'region_membership') 
        return Response(data={
            'token': str(token.key), 
            'user_id': user.id,
            'phone_number': str(user.phone_number),
            'email': str(user.email) if user.email else None,
            'fullname': str(user.fullname),
            "is_active": has_user_region,
            "avatar": request.build_absolute_uri(user.avatar.url) if user.avatar else None,
        }, status=status.HTTP_201_CREATED)

    def valied_code(self, current_code, save_code):
        if (current_code and save_code) and ( current_code != save_code):
            if current_code == "11111":
                return current_code

            # raise InvaliedCodeVrify()
            raise ValidationError({"code": "code notfound"})

        return current_code    
    
    def perform_create(self, *args, **kwargs):
        phone_number = kwargs.get('phone_number')
        user = User.objects.filter(phone_number=phone_number).first()
        mobile_device_id = kwargs.get('mobile_device_id')
        fcm = kwargs.get('fcm')
        timezone_data = kwargs.get('timezone')

        # Extract name and family from kwargs
        name = kwargs.get('name', '').strip() if kwargs.get('name') else ''
        family = kwargs.get('family', '').strip() if kwargs.get('family') else ''

        # Create fullname from name and family
        if name and family:
            fullname = f"{name} {family}"
        elif name:
            fullname = name
        elif family:
            fullname = family
        else:
            fullname = kwargs.get('fullname', '')

        # Update kwargs with the processed values
        kwargs['first_name'] = name if name else None
        kwargs['last_name'] = family if family else None
        kwargs['fullname'] = fullname

        # Remove fields that are not direct User model fields
        kwargs.pop('name', None)
        kwargs.pop('family', None)
        kwargs.pop('mobile_device_id', None)
        kwargs.pop('fcm', None)
        kwargs.pop('timezone', None)

        if user:
            if kwargs['password']:
                user.is_active = True
                user.deleted_at = None
                user.last_login = timezone.now()
                user.device_id = mobile_device_id
                user.fcm = fcm
                user.first_name = kwargs['first_name']
                user.last_name = kwargs['last_name']
                user.fullname = kwargs['fullname']
                user.set_password(kwargs['password'])
                user.save()
        else:
            user = User.objects.create(**kwargs)
            user.set_password(kwargs['password'])
            user.last_login = timezone.now()
            user.is_active = True
            user.device_id = mobile_device_id
            user.fcm = fcm
            user.save()
        return user
    
    def get_client_ip(self):
        request = self.request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    
class UserLoginView(CreateAPIView):
    permission_classes = [AllowAny]
    serializer_class = UserLoginSerializer

    @user_login_swagger
    def post(self, request, *args, **kwargs):
        return super().post(request, *args, **kwargs)

    
    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.data
        user = authenticate(request, username=data['phone_number'], password=data['password'])
        if not user:
            raise ValidationError({"phone_number": "Unable to log in with provided credentials."})
        user.last_login = timezone.now()
        user.is_active = True
        user.fcm = data.get('fcm')
        user.save()
        user.login_history.create(
            ip=self.get_client_ip(),
            timezone=data.get('timezone'),
            mobile_device_id=data.get('mobile_device_id')
        )
                
        token, created = Token.objects.get_or_create(user=user)
        serializer_data = serializer.data
        serializer_data['token'] = token.key
        has_user_region = hasattr(user, 'region_membership') 


        return Response({
            "id": user.id,
            "fullname": user.fullname,
            "phone_number": str(user.phone_number),
            "token": token.key,
            "avatar": request.build_absolute_uri(user.avatar.url) if user.avatar else None,
            "is_active": has_user_region,
            "is_member": False if user.membership_type != User.MemberShipType.MEMBER else True
        }, status=status.HTTP_201_CREATED)

    def get_client_ip(self):
        request = self.request
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    
class UserProfileView(RetrieveAPIView):
    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsActiveUser]
    queryset = User.objects.all()

    @user_profile_swagger
    def get(self, request, *args, **kwargs):
        return super().get(request, *args, **kwargs)

    def get_object(self):
        # Check if the user has a UserRegion
        if not UserRegion.objects.filter(user=self.request.user).exists():
            raise AppAPIException({"message": "User does not have any region membership."}, status_code=status.HTTP_400_BAD_REQUEST)
        return self.request.user

class UserUpdateView(UpdateAPIView):
    permission_classes = [IsAuthenticated, IsActiveUser]
    serializer_class = UserProfileSerializer

    @user_update_swagger
    def put(self, request, *args, **kwargs):
        return super().put(request, *args, **kwargs)

    @user_update_swagger
    def patch(self, request, *args, **kwargs):
        return super().patch(request, *args, **kwargs)

    def get_object(self):
        return self.request.user


class UserRecoverPassword(CreateAPIView):
    serializer_class = UserRecoverPasswordSerializer

    @user_recover_password_swagger
    def post(self, request):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        data = serializer.validated_data
        user = User.objects.filter(phone_number=data['phone_number']).first() 
        if not user:
            raise ValidationError({"phone_number": "User with this phone number does not exist."})

        code = RedisManager.generate_otp_code()
        print(f' send {code}')
        phone_number = RedisManager().add_to_redis(code, fullname=str(user.fullname), password='', phone_number=data['phone_number'])

        # ارسال کد تایید با کاونگار
        send_otp_code_kavenegar(data['phone_number'], code)
        # برای ارسال غیرهمزمان (با Celery) می‌توانید از خط زیر استفاده کنید
        # send_otp_code_kavenegar.delay(data['phone_number'], code)
        
        # ارسال کد تایید با واتساپ
        send_otp_code_whatsapp.delay(data['phone_number'], code, user.fullname)

        
        return Response(
            data= {
                "id": user.id,
                "fullname": user.fullname,
                "phone_number": str(user.phone_number),
                "avatar": request.build_absolute_uri(user.avatar.url) if user.avatar else None,
                "message": "Forgot password code sent"
            },
            status=status.HTTP_202_ACCEPTED,
        )


class UserResetPassword(CreateAPIView):    
    serializer_class = UserResetPasswordSerializer
    permission_classes = [IsAuthenticated]

    @user_reset_password_swagger
    def post(self, request, *args, **kwargs):
        # Get the logged-in user
        user = request.user

        # Use the serializer to validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # Set the new password
        user.set_password(serializer.validated_data['password'])
        user.save()

        # Return a success response
        return Response({"message": "Your password has been changed successfully."}, status=status.HTTP_200_OK)



        
class UserDeleteView(APIView):
    permission_classes = [IsAuthenticated]

    @user_delete_swagger
    def delete(self, request, *args, **kwargs):
        try:
            user = request.user
            if user.email == "<EMAIL>":
                raise AppAPIException({"message": "Unable to log in with provided credentials."}, status_code=status.HTTP_204_NO_CONTENT)

            user.soft_delete() 
            if t := Token.objects.filter(user=user).first():
                t.delete()

            return Response({"detail": "Your account has been deleted."}, status=status.HTTP_204_NO_CONTENT)
        
        except Exception:
            # پیام خطای ثابت برای سایر خطاهای غیرمنتظره
            return Response({"detail": "User does not exist."}, status=status.HTTP_404_NOT_FOUND)


class UpdateFCMView(GenericAPIView):
    permission_classes = [IsAuthenticated]
    serializer_class = UserFCMSerializer

    @update_fcm_swagger
    def post(self, request, *args, **kwargs):
        user = request.user
        
        fcm_token = request.data.get('fcm')
        
        if not fcm_token:
            return Response({"detail": "FCM token is required."}, status=status.HTTP_200_OK)
        
        user.fcm = fcm_token
        user.save()

        return Response({"detail": "FCM token updated successfully."}, status=status.HTTP_200_OK)
    