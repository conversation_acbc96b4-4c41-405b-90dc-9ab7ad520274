from apps.account.tasks import send_notification
from typing import List, Dict, Any, Optional, Union
from apps.account.models import User
from apps.account.models.notification import Notification
import logging

logger = logging.getLogger(__name__)

class NotificationService:
    """
    Service class for sending notifications across the application.
    This centralizes notification logic and provides consistent notification formats.
    """
    
    @staticmethod
    def _send_and_save_notification(user_id: int, title: str, body: str, data: Dict[str, Any] = None) -> bool:
        """
        Helper method to send a notification via FCM and save it to the database.
        
        Args:
            user_id: The ID of the user to send the notification to
            title: The notification title
            body: The notification body
            data: Additional data to include with the notification
            
        Returns:
            bool: True if notification was sent successfully, False otherwise
        """
        try:
            user = User.objects.get(id=user_id)
            fcm_token = user.fcm
            
            if not fcm_token:
                logger.warning(f"User {user_id} does not have an FCM token. Notification not sent.")
                return False
            
            # Save notification to database
            Notification.objects.create(
                user=user,
                title=title,
                message=body,
                data=data
            )
            
            # Send notification via FCM
            send_notification(
                ids=[fcm_token],
                title=title,
                body=body,
                data=data
            )
            
            return True
        except User.DoesNotExist:
            logger.error(f"User with ID {user_id} not found. Notification not sent.")
            return False
        except Exception as e:
            logger.error(f"Error sending notification to user {user_id}: {str(e)}")
            return False
    
    @staticmethod
    def send_deposit_request_approved(user_id: int, deposit_id: int) -> None:
        """
        Send notification when a deposit creation request is approved.
        
        Args:
            user_id: The ID of the user who made the request
            deposit_id: The ID of the approved deposit
        """
        title = "تایید درخواست عضویت"
        body = "درخواست عضویت شما مورد تأیید قرار گرفت."
        data = {
            "model": "Deposit",
            "id": deposit_id
        }
        
        NotificationService._send_and_save_notification(
            user_id=user_id,
            title=title,
            body=body,
            data=data
        )
    
    @staticmethod
    def send_deposit_request_rejected(user_id: int) -> None:
        """
        Send notification when a deposit creation request is rejected.
        
        Args:
            user_id: The ID of the user who made the request
        """
        title = "عدم تایید عضویت"
        body = "درخواست عضویت شما رد شد. لطفاً با مدیر صندوق تماس بگیرید."
        data = {
            "help": "در صورت رد عضویت، کاربر به ناوبر صندوق ها منتقل شود.",
            "model": "DepositRequest"
        }
        
        NotificationService._send_and_save_notification(
            user_id=user_id,
            title=title,
            body=body,
            data=data
        )
            
    @staticmethod
    def send_loan_request_approved(user_id: int, deposit_id: int) -> None:
        """
        Send notification when a loan request is approved.
        
        Args:
            user_id: The ID of the user who made the request
            deposit_id: The ID of the deposit associated with the loan
        """
        title = "تایید درخواست وام"
        body = "وام شما مورد تأیید قرار گرفت و در حال پرداخت است."
        data = {
            "help": "انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        NotificationService._send_and_save_notification(
            user_id=user_id,
            title=title,
            body=body,
            data=data
        )
    
    @staticmethod
    def send_loan_request_rejected(user_id: int, deposit_id: int) -> None:
        """
        Send notification when a loan request is rejected.
        
        Args:
            user_id: The ID of the user who made the request
            deposit_id: The ID of the deposit associated with the loan
        """
        title = "عدم تایید درخواست وام"
        body = "درخواست وام شما رد شد. لطفاً دلایل را در بخش تیکت‌ها بررسی کنید"
        data = {
            "help": "انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        NotificationService._send_and_save_notification(
            user_id=user_id,
            title=title,
            body=body,
            data=data
        )
            
    @staticmethod
    def send_payment_created_notification_to_owner(deposit_id: int) -> None:
        """
        Send notification to the deposit owner when a new payment is created.
        
        Args:
            deposit_id: The ID of the deposit for which the payment was made
        """
        from apps.deposit.models import DepositMembership
        
        title = "افزایش موجودی صندوق"
        body = "مبلغ جدیدی به صندوق شما اضافه شد."
        data = {
            "help": "واریز پس‌انداز فقط برای مدیر - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        try:
            # Find the owner of the deposit
            owner_membership = DepositMembership.objects.filter(
                deposit_id=deposit_id,
                role=DepositMembership.Role.OWNER,
                is_active=True
            ).first()
            
            if not owner_membership:
                logger.warning(f"No owner found for deposit {deposit_id}. Notification not sent.")
                return
                
            owner_id = owner_membership.user_id
            
            # Send notification to the deposit owner
            NotificationService._send_and_save_notification(
                user_id=owner_id,
                title=title,
                body=body,
                data=data
            )
        except Exception as e:
            logger.error(f"Error sending notification to deposit owner: {str(e)}")
            
    @staticmethod
    def send_withdrawal_approved_notification_to_owner(deposit_id: int) -> None:
        """
        Send notification to the deposit owner when a withdrawal request is approved.
        
        Args:
            deposit_id: The ID of the deposit from which the withdrawal was made
        """
        from apps.deposit.models import DepositMembership
        
        title = "کاهش موجودی صندوق پس انداز"
        body = "برداشت شما از صندوق با موفقیت انجام شد."
        data = {
            "help": "برداشت از پس‌انداز (فقط برای مدیر) - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        try:
            # Find the owner of the deposit
            owner_membership = DepositMembership.objects.filter(
                deposit_id=deposit_id,
                role=DepositMembership.Role.OWNER,
                is_active=True
            ).first()
            
            if not owner_membership:
                logger.warning(f"No owner found for deposit {deposit_id}. Notification not sent.")
                return
                
            owner_id = owner_membership.user_id
            
            # Send notification to the deposit owner
            NotificationService._send_and_save_notification(
                user_id=owner_id,
                title=title,
                body=body,
                data=data
            )
        except Exception as e:
            logger.error(f"Error sending notification to deposit owner: {str(e)}")
            
    @staticmethod
    def send_lottery_announcement_to_all_members(deposit_id: int) -> None:
        """
        Send notification to all deposit members when a lottery winner is announced or lottery is completed.
        
        Args:
            deposit_id: The ID of the deposit for which the lottery was conducted
        """
        from apps.deposit.models import DepositMembership
        
        title = "اعلام برندگان قرعه کشی"
        body = "قرعه‌کشی این دوره انجام شد. برای مشاهده نتایج وارد شوید."
        data = {
            "help": "اعلام نتایج قرعه‌کشی - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        try:
            # Find all active members of the deposit
            active_memberships = DepositMembership.objects.filter(
                deposit_id=deposit_id,
                is_active=True
            )
            
            if not active_memberships.exists():
                logger.warning(f"No active members found for deposit {deposit_id}. Notification not sent.")
                return
            
            # Send notification to each member individually and save to database
            for membership in active_memberships:
                try:
                    NotificationService._send_and_save_notification(
                        user_id=membership.user_id,
                        title=title,
                        body=body,
                        data=data
                    )
                except Exception as e:
                    logger.warning(f"Error sending notification to user {membership.user_id}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error sending notification to deposit members: {str(e)}")
            
    @staticmethod
    def send_lottery_winner_notification(deposit_id: int, winner_user_id: int) -> None:
        """
        Send notification to the lottery winner.
        
        Args:
            deposit_id: The ID of the deposit for which the lottery was conducted
            winner_user_id: The ID of the user who won the lottery
        """
        title = "حضور شما در لیست برندگان قرعه کشی"
        body = "تبریک! شما برنده قرعه‌کشی صندوق شدید."
        data = {
            "help": "برنده شدن در قرعه‌کشی - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        NotificationService._send_and_save_notification(
            user_id=winner_user_id,
            title=title,
            body=body,
            data=data
        )
            
    @staticmethod
    def send_voting_poll_created_notification(deposit_id: int) -> None:
        """
        Send notification to all deposit members when a new voting poll is created.
        
        Args:
            deposit_id: The ID of the deposit for which the voting poll was created
        """
        from apps.deposit.models import DepositMembership
        
        title = "رای گیری در حال حاضر فعال است"
        body = "رأی‌گیری جدید در صندوق آغاز شد. لطفاً مشارکت کنید."
        data = {
            "help": "شروع رأی‌گیری - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        try:
            # Find all active members of the deposit
            active_memberships = DepositMembership.objects.filter(
                deposit_id=deposit_id,
                is_active=True
            )
            
            if not active_memberships.exists():
                logger.warning(f"No active members found for deposit {deposit_id}. Notification not sent.")
                return
            
            # Send notification to each member individually and save to database
            for membership in active_memberships:
                try:
                    NotificationService._send_and_save_notification(
                        user_id=membership.user_id,
                        title=title,
                        body=body,
                        data=data
                    )
                except Exception as e:
                    logger.warning(f"Error sending notification to user {membership.user_id}: {str(e)}")
                    
        except Exception as e:
            logger.error(f"Error sending notification to deposit members: {str(e)}")
            
    @staticmethod
    def send_new_ticket_message_notification(user_ids: List[int]) -> None:
        """
        Send notification to users about a new ticket message.
        
        Args:
            user_ids: List of user IDs to send the notification to
        """
        title = "دریافت تیکت جدید"
        body = "شما یک تیکت جدید دارد. برای مشاهده کلیک کنید"
        data = {
            "help": "تیکت جدید - انتقال به تیکت ها",
            "model": "TicketList"
        }
        
        try:
            # Send notification to each user individually and save to database
            for user_id in user_ids:
                try:
                    NotificationService._send_and_save_notification(
                        user_id=user_id,
                        title=title,
                        body=body,
                        data=data
                    )
                except Exception as e:
                    logger.warning(f"Error sending notification to user {user_id}: {str(e)}")
        except Exception as e:
            logger.error(f"Error sending notification about new ticket message: {str(e)}")
            
    @staticmethod
    def send_withdrawal_request_created_notification(user_id: int, deposit_id: int) -> None:
        """
        Send notification to a user when their withdrawal request is created.
        
        Args:
            user_id: The ID of the user who created the withdrawal request
            deposit_id: The ID of the deposit from which the withdrawal was requested
        """
        title = "ثبت درخواست برداشت"
        body = "درخواست شما برای برداشت از صندوق ثبت شد."
        data = {
            "help": "درخواست برداشت - انتقال به سینگل پیج صندوق",
            "model": "Deposit",
            "id": deposit_id
        }
        
        NotificationService._send_and_save_notification(
            user_id=user_id,
            title=title,
            body=body,
            data=data
        )