<!DOCTYPE html>
<html lang="fa" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مترجم قطره - Qatreh Translator</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .translator-container {
            padding: 40px;
        }

        .language-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            gap: 20px;
        }

        .language-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
            flex: 1;
        }

        .language-group label {
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .language-select {
            padding: 12px 16px;
            border: 2px solid #e1e5e9;
            border-radius: 10px;
            font-size: 1rem;
            background: white;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .language-select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .swap-button {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            color: white;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 25px;
        }

        .swap-button:hover {
            transform: rotate(180deg) scale(1.1);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .text-areas {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .text-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .text-group label {
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .text-area {
            min-height: 200px;
            padding: 20px;
            border: 2px solid #e1e5e9;
            border-radius: 15px;
            font-size: 1rem;
            line-height: 1.6;
            resize: vertical;
            transition: all 0.3s ease;
            font-family: inherit;
        }

        .text-area:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        #result-text {
            background-color: #f8f9fa;
            cursor: default;
        }

        .translate-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            font-size: 1.1rem;
            font-weight: 600;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: block;
            margin: 0 auto;
            min-width: 200px;
        }

        .translate-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .translate-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
            color: #667eea;
        }

        .loading.show {
            display: block;
        }

        .error {
            background-color: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border: 1px solid #fcc;
            display: none;
        }

        .error.show {
            display: block;
        }

        .character-count {
            text-align: right;
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        @media (max-width: 768px) {
            .language-selector {
                flex-direction: column;
                gap: 15px;
            }

            .text-areas {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .swap-button {
                margin-top: 0;
                transform: rotate(90deg);
            }

            .header h1 {
                font-size: 2rem;
            }

            .translator-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 مترجم قطره</h1>
            <p>ترجمه آنلاین با کیفیت بالا - Qatreh Online Translator</p>
            <div style="margin-top: 15px; font-size: 0.9rem; opacity: 0.8;">
                <strong>میانبرهای کیبورد:</strong>
                Ctrl+Enter: ترجمه | Ctrl+Shift+C: کپی | Ctrl+Shift+S: تعویض زبان
            </div>
        </div>

        <div class="translator-container">
            <div class="language-selector">
                <div class="language-group">
                    <label for="source-lang">زبان مبدا:</label>
                    <select id="source-lang" class="language-select">
                        {% for code, name in languages.items %}
                            <option value="{{ code }}" {% if code == 'en' %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <button type="button" class="swap-button" id="swap-languages" title="تعویض زبان‌ها">
                    ⇄
                </button>

                <div class="language-group">
                    <label for="target-lang">زبان مقصد:</label>
                    <select id="target-lang" class="language-select">
                        {% for code, name in languages.items %}
                            <option value="{{ code }}" {% if code == 'fa' %}selected{% endif %}>{{ name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>

            <div class="text-areas">
                <div class="text-group">
                    <label for="source-text">متن مبدا:</label>
                    <textarea 
                        id="source-text" 
                        class="text-area" 
                        placeholder="متن خود را اینجا وارد کنید..."
                        maxlength="5000"
                    ></textarea>
                    <div class="character-count">
                        <span id="char-count">0</span> / 5000
                    </div>
                </div>

                <div class="text-group">
                    <label for="result-text">ترجمه:</label>
                    <textarea 
                        id="result-text" 
                        class="text-area" 
                        placeholder="ترجمه اینجا نمایش داده می‌شود..."
                        readonly
                    ></textarea>
                </div>
            </div>

            <div class="loading" id="loading">
                <p>🔄 در حال ترجمه...</p>
            </div>

            <div class="error" id="error-message"></div>

            <div style="display: flex; gap: 15px; justify-content: center; align-items: center;">
                <button type="button" class="translate-button" id="translate-btn">
                    🚀 ترجمه کن
                </button>
                <button type="button" class="translate-button" id="copy-btn" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); min-width: 150px;" disabled>
                    📋 کپی نتیجه
                </button>
            </div>
        </div>
    </div>

    <script>
        // Get DOM elements
        const sourceText = document.getElementById('source-text');
        const resultText = document.getElementById('result-text');
        const sourceLang = document.getElementById('source-lang');
        const targetLang = document.getElementById('target-lang');
        const translateBtn = document.getElementById('translate-btn');
        const copyBtn = document.getElementById('copy-btn');
        const swapBtn = document.getElementById('swap-languages');
        const loading = document.getElementById('loading');
        const errorMessage = document.getElementById('error-message');
        const charCount = document.getElementById('char-count');

        // Character counter
        sourceText.addEventListener('input', function() {
            charCount.textContent = this.value.length;
        });

        // Swap languages
        swapBtn.addEventListener('click', function() {
            const sourceValue = sourceLang.value;
            const targetValue = targetLang.value;
            const sourceTextValue = sourceText.value;
            const resultTextValue = resultText.value;

            sourceLang.value = targetValue;
            targetLang.value = sourceValue;
            sourceText.value = resultTextValue;
            resultText.value = sourceTextValue;
        });

        // Hide error message when typing and clear result
        sourceText.addEventListener('input', function() {
            errorMessage.classList.remove('show');

            // اگر متن پاک شد، نتیجه رو هم پاک کن
            if (!this.value.trim()) {
                resultText.value = '';
                copyBtn.disabled = true;
            }
        });

        // Translate function
        async function translateText() {
            const text = sourceText.value.trim();

            if (!text) {
                showError('لطفاً متنی برای ترجمه وارد کنید');
                return;
            }

            if (sourceLang.value === targetLang.value) {
                showError('زبان مبدا و مقصد نمی‌توانند یکسان باشند');
                return;
            }

            // Show loading
            loading.classList.add('show');
            translateBtn.disabled = true;
            errorMessage.classList.remove('show');
            resultText.value = '';

            try {
                // آماده‌سازی داده‌های درخواست
                const requestData = {
                    source: sourceLang.value,
                    target: targetLang.value,
                    text: text
                };

                // لاگ کردن درخواست
                logRequest(requestData);

                // درخواست مستقیم به one-api.ir با retry mechanism
                const data = await retryTranslation(requestData);

                // بررسی موفقیت ترجمه
                if (data.status === 200 && data.result) {
                    resultText.value = data.result;

                    // لاگ موفقیت
                    console.log('✅ ترجمه موفق:', {
                        متن_اصلی: data.original_text,
                        ترجمه: data.result,
                        از: data.source,
                        به: data.target,
                        زمان: new Date().toLocaleTimeString('fa-IR')
                    });

                    showSuccess(`ترجمه از ${sourceLang.options[sourceLang.selectedIndex].text} به ${targetLang.options[targetLang.selectedIndex].text} انجام شد`);

                    // فعال کردن دکمه کپی
                    copyBtn.disabled = false;
                } else {
                    const errorMsg = data.error || 'خطا در ترجمه - پاسخ نامعتبر';
                    showError(errorMsg);
                    console.error('❌ خطا در API:', data);

                    // غیرفعال کردن دکمه کپی
                    copyBtn.disabled = true;
                }
            } catch (error) {
                console.error('❌ خطا در ترجمه:', error);
                showError('خطا در اتصال به سرور ترجمه');
            } finally {
                loading.classList.remove('show');
                translateBtn.disabled = false;
            }
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.add('show');
            console.error('❌ خطا:', message);
        }

        // نمایش پیام موفقیت (اختیاری)
        function showSuccess(message) {
            console.log('✅ موفق:', message);
            // می‌توانیم یک toast notification اضافه کنیم
        }

        // تابع کمکی برای لاگ کردن درخواست
        function logRequest(data) {
            console.log('📤 درخواست ترجمه:', {
                از: data.source,
                به: data.target,
                متن: data.text.substring(0, 50) + (data.text.length > 50 ? '...' : ''),
                طول: data.text.length,
                زمان: new Date().toLocaleTimeString('fa-IR')
            });
        }

        // تابع retry برای درخواست‌های ناموفق - مستقیم به one-api.ir
        async function retryTranslation(requestData, maxRetries = 2) {
            for (let attempt = 1; attempt <= maxRetries; attempt++) {
                try {
                    console.log(`🔄 تلاش ${attempt} از ${maxRetries}`);

                    const response = await fetch('https://api.one-api.ir/translate/v1/google/', {
                        method: 'POST',
                        headers: {
                            'accept': 'application/json',
                            'one-api-token': '346197:674e15cce6561',
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            source: requestData.source,
                            target: requestData.target,
                            text: requestData.text
                        })
                    });

                    if (response.ok) {
                        const data = await response.json();
                        // تبدیل response به فرمت مورد انتظار
                        return {
                            status: data.status || 200,
                            result: data.result,
                            source: requestData.source,
                            target: requestData.target,
                            original_text: requestData.text
                        };
                    }

                    if (attempt === maxRetries) {
                        throw new Error(`درخواست پس از ${maxRetries} تلاش ناموفق بود`);
                    }

                    // انتظار کوتاه قبل از تلاش مجدد
                    await new Promise(resolve => setTimeout(resolve, 1000 * attempt));

                } catch (error) {
                    if (attempt === maxRetries) {
                        throw error;
                    }
                    console.warn(`⚠️ تلاش ${attempt} ناموفق:`, error.message);
                }
            }
        }

        // حذف شد - دیگه نیازی به CSRF token نیست چون مستقیم به one-api.ir درخواست میزنیم

        // تابع کپی کردن نتیجه
        async function copyResult() {
            const text = resultText.value.trim();
            if (!text) {
                showError('هیچ متنی برای کپی وجود ندارد');
                return;
            }

            try {
                await navigator.clipboard.writeText(text);

                // تغییر موقت متن دکمه
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '✅ کپی شد!';
                copyBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';

                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                    copyBtn.style.background = 'linear-gradient(135deg, #28a745 0%, #20c997 100%)';
                }, 2000);

                console.log('📋 متن کپی شد:', text.substring(0, 50) + (text.length > 50 ? '...' : ''));

            } catch (error) {
                console.error('❌ خطا در کپی:', error);
                showError('خطا در کپی کردن متن');
            }
        }

        // Event listeners
        translateBtn.addEventListener('click', translateText);
        copyBtn.addEventListener('click', copyResult);

        // Keyboard shortcuts
        sourceText.addEventListener('keydown', function(e) {
            // Ctrl+Enter برای ترجمه
            if (e.ctrlKey && e.key === 'Enter') {
                e.preventDefault();
                translateText();
            }
        });

        // Ctrl+C در result area برای کپی
        resultText.addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'c' && this.value.trim()) {
                copyResult();
            }
        });

        // Global keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Ctrl+Shift+C برای کپی نتیجه
            if (e.ctrlKey && e.shiftKey && e.key === 'C') {
                e.preventDefault();
                if (!copyBtn.disabled) {
                    copyResult();
                }
            }

            // Ctrl+Shift+S برای swap languages
            if (e.ctrlKey && e.shiftKey && e.key === 'S') {
                e.preventDefault();
                swapBtn.click();
            }
        });

        // Auto-translate after typing stops (debounced)
        let typingTimer;
        sourceText.addEventListener('input', function() {
            clearTimeout(typingTimer);
            if (this.value.trim()) {
                typingTimer = setTimeout(translateText, 1000);
            }
        });
    </script>
</body>
</html>
